<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.rustycluster.client</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">com.rustycluster.client</span></div><h1>com.rustycluster.client</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">638 of 1,087</td><td class="ctr2">41%</td><td class="bar">8 of 10</td><td class="ctr2">20%</td><td class="ctr1">56</td><td class="ctr2">92</td><td class="ctr1">175</td><td class="ctr2">334</td><td class="ctr1">52</td><td class="ctr2">87</td><td class="ctr1">1</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="RustyClusterClient.java.html" class="el_source">RustyClusterClient.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="390" alt="390"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="251" alt="251"/></td><td class="ctr2" id="c1">39%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">33%</td><td class="ctr1" id="f0">30</td><td class="ctr2" id="g0">53</td><td class="ctr1" id="h0">101</td><td class="ctr2" id="i0">175</td><td class="ctr1" id="j0">28</td><td class="ctr2" id="k0">50</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="RustyClusterAsyncClient.java.html" class="el_source">RustyClusterAsyncClient.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="46" height="10" title="248" alt="248"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">26</td><td class="ctr2" id="g1">26</td><td class="ctr1" id="h1">74</td><td class="ctr2" id="i2">74</td><td class="ctr1" id="j1">24</td><td class="ctr2" id="k1">24</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="BatchOperationBuilder.java.html" class="el_source">BatchOperationBuilder.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="198" alt="198"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">13</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">85</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>