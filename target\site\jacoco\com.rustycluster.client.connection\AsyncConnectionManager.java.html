<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">AsyncConnectionManager.java</span></div><h1>AsyncConnectionManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
<span class="nc" id="L24">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final Executor executor;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L39">        this(config, new AsyncConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L40">    }</span>

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="nc" id="L48">    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {</span>
<span class="nc" id="L49">        this.config = config;</span>
<span class="nc" id="L50">        this.connectionPool = connectionPool;</span>
<span class="nc" id="L51">        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="nc" id="L54">        this.sortedNodes = config.getNodes().stream()</span>
<span class="nc" id="L55">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="nc" id="L56">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="nc" id="L59">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

<span class="nc" id="L61">        logger.info(&quot;AsyncConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="nc" id="L62">    }</span>

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation) {
<span class="nc" id="L72">        return executeWithFailoverAsync(operation, OperationType.READ, 0);</span>
    }

    /**
     * Execute an operation asynchronously with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType) {
<span class="nc" id="L84">        return executeWithFailoverAsync(operation, operationType, 0);</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, OperationType operationType, int retryCount) {
<span class="nc bnc" id="L88" title="All 2 branches missed.">        if (retryCount &gt; config.getMaxRetries()) {</span>
<span class="nc" id="L89">            return CompletableFuture.failedFuture(</span>
                new NoAvailableNodesException(&quot;Operation failed after &quot; + retryCount + &quot; retries&quot;));
        }

        // Determine timeout based on operation type
<span class="nc bnc" id="L94" title="All 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="nc" id="L95">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="nc" id="L96">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="nc" id="L97">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="nc" id="L100">        NodeConfig node = currentNode.get();</span>

<span class="nc" id="L102">        return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L103">            .thenCompose(stub -&gt; {</span>
                try {
                    // Apply deadline per operation to avoid expired deadline issues
<span class="nc" id="L106">                    KeyValueServiceGrpc.KeyValueServiceFutureStub stubWithDeadline =</span>
<span class="nc" id="L107">                        stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="nc" id="L108">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stubWithDeadline);</span>
<span class="nc" id="L109">                    return toCompletableFuture(listenableFuture)</span>
<span class="nc" id="L110">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L111">                            connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L112">                        });</span>
<span class="nc" id="L113">                } catch (Exception e) {</span>
<span class="nc" id="L114">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L115">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="nc" id="L118">            .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L119">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>

                // Try to find the next available node
<span class="nc" id="L122">                var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">                if (nextNode != null) {</span>
<span class="nc" id="L124">                    currentNode.set(nextNode);</span>
<span class="nc" id="L125">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>
                }

<span class="nc" id="L128">                throw new RuntimeException(throwable);</span>
            })
<span class="nc" id="L130">            .handle((result, throwable) -&gt; {</span>
<span class="nc bnc" id="L131" title="All 2 branches missed.">                if (throwable != null) {</span>
                    // Retry with delay
<span class="nc" id="L133">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L134">                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),</span>
                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)
<span class="nc" id="L136">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L137">                    return delay.thenCompose(v -&gt; executeWithFailoverAsync(operation, operationType, retryCount + 1));</span>
                }
<span class="nc" id="L139">                return CompletableFuture.completedFuture(result);</span>
            })
<span class="nc bnc" id="L141" title="All 2 branches missed.">            .thenCompose(future -&gt; future instanceof CompletableFuture ?</span>
<span class="nc" id="L142">                (CompletableFuture&lt;T&gt;) future : CompletableFuture.completedFuture((T) future));</span>
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L149">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>

<span class="nc" id="L151">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L153">                completableFuture.complete(listenableFuture.get());</span>
<span class="nc" id="L154">            } catch (Exception e) {</span>
<span class="nc" id="L155">                completableFuture.completeExceptionally(e);</span>
<span class="nc" id="L156">            }</span>
<span class="nc" id="L157">        }, executor);</span>

<span class="nc" id="L159">        return completableFuture;</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="nc" id="L170">        var samePriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L171" title="All 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="nc" id="L172">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L173">                .findFirst();</span>

<span class="nc bnc" id="L175" title="All 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L176">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="nc" id="L180">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="nc" id="L182">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L183">                .findFirst();</span>

<span class="nc bnc" id="L185" title="All 2 branches missed.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="nc" id="L186">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="nc" id="L190">        return sortedNodes.stream()</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="nc" id="L192">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L193">                .findFirst()</span>
<span class="nc" id="L194">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
<span class="nc" id="L207">            return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L208">                .thenApply(stub -&gt; {</span>
<span class="nc" id="L209">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L210">                    return true;</span>
                })
<span class="nc" id="L212">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L213">                    logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L214">                    return false;</span>
                })
<span class="nc" id="L216">                .join(); // This is not ideal for async, but needed for this interface</span>
<span class="nc" id="L217">        } catch (Exception e) {</span>
<span class="nc" id="L218">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L219">            return false;</span>
        }
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="nc" id="L228">        connectionPool.close();</span>
<span class="nc" id="L229">        logger.info(&quot;AsyncConnectionManager closed&quot;);</span>
<span class="nc" id="L230">    }</span>

    /**
     * Functional interface for async client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>