<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">AsyncConnectionManager.java</span></div><h1>AsyncConnectionManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.google.common.util.concurrent.ListenableFuture;
import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages asynchronous connections to RustyCluster nodes, handling prioritization and failover.
 */
public class AsyncConnectionManager implements AutoCloseable {
<span class="nc" id="L23">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final AsyncConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final Executor executor;

    /**
     * Create a new AsyncConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L38">        this(config, new AsyncConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L39">    }</span>

    /**
     * Create a new AsyncConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="nc" id="L47">    AsyncConnectionManager(RustyClusterClientConfig config, AsyncConnectionPool connectionPool) {</span>
<span class="nc" id="L48">        this.config = config;</span>
<span class="nc" id="L49">        this.connectionPool = connectionPool;</span>
<span class="nc" id="L50">        this.executor = ForkJoinPool.commonPool(); // Use common pool for async operations</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="nc" id="L53">        this.sortedNodes = config.getNodes().stream()</span>
<span class="nc" id="L54">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="nc" id="L55">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="nc" id="L58">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

<span class="nc" id="L60">        logger.info(&quot;AsyncConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="nc" id="L61">    }</span>

    /**
     * Execute an operation asynchronously with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return CompletableFuture that completes with the result of the operation
     */
    public &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation) {
<span class="nc" id="L71">        return executeWithFailoverAsync(operation, 0);</span>
    }

    private &lt;T&gt; CompletableFuture&lt;T&gt; executeWithFailoverAsync(AsyncClientOperation&lt;T&gt; operation, int retryCount) {
<span class="nc bnc" id="L75" title="All 2 branches missed.">        if (retryCount &gt; config.getMaxRetries()) {</span>
<span class="nc" id="L76">            return CompletableFuture.failedFuture(</span>
<span class="nc" id="L77">                new NoAvailableNodesException(&quot;Operation failed after &quot; + retryCount + &quot; retries&quot;));</span>
        }

<span class="nc" id="L80">        NodeConfig node = currentNode.get();</span>

<span class="nc" id="L82">        return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L83">            .thenCompose(stub -&gt; {</span>
                try {
<span class="nc" id="L85">                    ListenableFuture&lt;T&gt; listenableFuture = operation.execute(stub);</span>
<span class="nc" id="L86">                    return toCompletableFuture(listenableFuture)</span>
<span class="nc" id="L87">                        .whenComplete((result, throwable) -&gt; {</span>
<span class="nc" id="L88">                            connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L89">                        });</span>
<span class="nc" id="L90">                } catch (Exception e) {</span>
<span class="nc" id="L91">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L92">                    return CompletableFuture.failedFuture(e);</span>
                }
            })
<span class="nc" id="L95">            .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L96">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, throwable.getMessage());</span>

                // Try to find the next available node
<span class="nc" id="L99">                var nextNode = findNextAvailableNode(node);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">                if (nextNode != null) {</span>
<span class="nc" id="L101">                    currentNode.set(nextNode);</span>
<span class="nc" id="L102">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>
                }

<span class="nc" id="L105">                throw new RuntimeException(throwable);</span>
            })
<span class="nc" id="L107">            .handle((result, throwable) -&gt; {</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">                if (throwable != null) {</span>
                    // Retry with delay
<span class="nc" id="L110">                    CompletableFuture&lt;Void&gt; delay = new CompletableFuture&lt;&gt;();</span>
<span class="nc" id="L111">                    CompletableFuture.delayedExecutor(config.getRetryDelayMs(),</span>
<span class="nc" id="L112">                            java.util.concurrent.TimeUnit.MILLISECONDS, executor)</span>
<span class="nc" id="L113">                        .execute(() -&gt; delay.complete(null));</span>
<span class="nc" id="L114">                    return delay.thenCompose(v -&gt; executeWithFailoverAsync(operation, retryCount + 1));</span>
                }
<span class="nc" id="L116">                return CompletableFuture.completedFuture(result);</span>
            })
<span class="nc bnc" id="L118" title="All 2 branches missed.">            .thenCompose(future -&gt; future instanceof CompletableFuture ?</span>
<span class="nc" id="L119">                (CompletableFuture&lt;T&gt;) future : CompletableFuture.completedFuture((T) future));</span>
    }

    /**
     * Convert ListenableFuture to CompletableFuture.
     */
    private &lt;T&gt; CompletableFuture&lt;T&gt; toCompletableFuture(ListenableFuture&lt;T&gt; listenableFuture) {
<span class="nc" id="L126">        CompletableFuture&lt;T&gt; completableFuture = new CompletableFuture&lt;&gt;();</span>

<span class="nc" id="L128">        listenableFuture.addListener(() -&gt; {</span>
            try {
<span class="nc" id="L130">                completableFuture.complete(listenableFuture.get());</span>
<span class="nc" id="L131">            } catch (Exception e) {</span>
<span class="nc" id="L132">                completableFuture.completeExceptionally(e);</span>
            }
<span class="nc" id="L134">        }, executor);</span>

<span class="nc" id="L136">        return completableFuture;</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="nc" id="L147">        var samePriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L148" title="All 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="nc" id="L149">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L150">                .findFirst();</span>

<span class="nc bnc" id="L152" title="All 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L153">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="nc" id="L157">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="nc" id="L159">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L160">                .findFirst();</span>

<span class="nc bnc" id="L162" title="All 2 branches missed.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="nc" id="L163">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="nc" id="L167">        return sortedNodes.stream()</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="nc" id="L169">                .filter(this::isNodeAvailable)</span>
<span class="nc" id="L170">                .findFirst()</span>
<span class="nc" id="L171">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
        try {
            // For async operations, we'll do a simple check
            // In a real implementation, you might want to perform an async health check
<span class="nc" id="L184">            return connectionPool.borrowStubAsync(node)</span>
<span class="nc" id="L185">                .thenApply(stub -&gt; {</span>
<span class="nc" id="L186">                    connectionPool.returnStub(node, stub);</span>
<span class="nc" id="L187">                    return true;</span>
                })
<span class="nc" id="L189">                .exceptionally(e -&gt; {</span>
<span class="nc" id="L190">                    logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L191">                    return false;</span>
                })
<span class="nc" id="L193">                .join(); // This is not ideal for async, but needed for this interface</span>
<span class="nc" id="L194">        } catch (Exception e) {</span>
<span class="nc" id="L195">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="nc" id="L196">            return false;</span>
        }
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="nc" id="L205">        connectionPool.close();</span>
<span class="nc" id="L206">        logger.info(&quot;AsyncConnectionManager closed&quot;);</span>
<span class="nc" id="L207">    }</span>

    /**
     * Functional interface for async client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface AsyncClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return ListenableFuture with the result of the operation
         * @throws Exception If the operation fails
         */
        ListenableFuture&lt;T&gt; execute(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>