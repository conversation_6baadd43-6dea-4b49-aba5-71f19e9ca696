<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_class">AsyncConnectionManager</span></div><h1>AsyncConnectionManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">407 of 407</td><td class="ctr2">0%</td><td class="bar">24 of 24</td><td class="ctr2">0%</td><td class="ctr1">37</td><td class="ctr2">37</td><td class="ctr1">92</td><td class="ctr2">92</td><td class="ctr1">24</td><td class="ctr2">24</td></tr></tfoot><tbody><tr><td id="a5"><a href="AsyncConnectionManager.java.html#L95" class="el_method">executeWithFailoverAsync(AsyncConnectionManager.AsyncClientOperation, OperationType, int)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="61" alt="61"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="AsyncConnectionManager.java.html#L49" class="el_method">AsyncConnectionManager(RustyClusterClientConfig, AsyncConnectionPool)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="52" alt="52"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="AsyncConnectionManager.java.html#L177" class="el_method">findNextAvailableNode(NodeConfig)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="98" height="10" title="50" alt="50"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h0">17</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="AsyncConnectionManager.java.html#L113" class="el_method">lambda$executeWithFailoverAsync$2(long, AsyncConnectionManager.AsyncClientOperation, NodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="28" alt="28"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a13"><a href="AsyncConnectionManager.java.html#L138" class="el_method">lambda$executeWithFailoverAsync$6(AsyncConnectionManager.AsyncClientOperation, OperationType, int, Object, Throwable)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="27" alt="27"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="AsyncConnectionManager.java.html#L126" class="el_method">lambda$executeWithFailoverAsync$3(NodeConfig, Throwable)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="25" alt="25"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="AsyncConnectionManager.java.html#L214" class="el_method">isNodeAvailable(NodeConfig)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="24" alt="24"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a23"><a href="AsyncConnectionManager.java.html#L156" class="el_method">toCompletableFuture(ListenableFuture)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="13" alt="13"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a17"><a href="AsyncConnectionManager.java.html#L178" class="el_method">lambda$findNextAvailableNode$9(NodeConfig, NodeConfig)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a21"><a href="AsyncConnectionManager.java.html#L160" class="el_method">lambda$toCompletableFuture$8(CompletableFuture, ListenableFuture)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="12" alt="12"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a15"><a href="AsyncConnectionManager.java.html#L188" class="el_method">lambda$findNextAvailableNode$10(NodeConfig, NodeConfig)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="11" alt="11"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="AsyncConnectionManager.java.html#L235" class="el_method">close()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="10" alt="10"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a1"><a href="AsyncConnectionManager.java.html#L40" class="el_method">AsyncConnectionManager(RustyClusterClientConfig, AuthenticationManager)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a19"><a href="AsyncConnectionManager.java.html#L220" class="el_method">lambda$isNodeAvailable$13(NodeConfig, Throwable)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="9" alt="9"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a18"><a href="AsyncConnectionManager.java.html#L216" class="el_method">lambda$isNodeAvailable$12(NodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a16"><a href="AsyncConnectionManager.java.html#L198" class="el_method">lambda$findNextAvailableNode$11(NodeConfig, NodeConfig)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a14"><a href="AsyncConnectionManager.java.html#L148" class="el_method">lambda$executeWithFailoverAsync$7(CompletableFuture)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a12"><a href="AsyncConnectionManager.java.html#L144" class="el_method">lambda$executeWithFailoverAsync$5(AsyncConnectionManager.AsyncClientOperation, OperationType, int, Void)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a3"><a href="AsyncConnectionManager.java.html#L79" class="el_method">executeWithFailoverAsync(AsyncConnectionManager.AsyncClientOperation)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a4"><a href="AsyncConnectionManager.java.html#L91" class="el_method">executeWithFailoverAsync(AsyncConnectionManager.AsyncClientOperation, OperationType)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a8"><a href="AsyncConnectionManager.java.html#L118" class="el_method">lambda$executeWithFailoverAsync$1(NodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub, Object, Throwable)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="6" alt="6"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a11"><a href="AsyncConnectionManager.java.html#L143" class="el_method">lambda$executeWithFailoverAsync$4(CompletableFuture)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a20"><a href="AsyncConnectionManager.java.html#L56" class="el_method">lambda$new$0(NodeConfig)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a22"><a href="AsyncConnectionManager.java.html#L24" class="el_method">static {...}</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>