<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.rustycluster.client.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">com.rustycluster.client.connection</span></div><h1>com.rustycluster.client.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">958 of 1,193</td><td class="ctr2">19%</td><td class="bar">40 of 60</td><td class="ctr2">33%</td><td class="ctr1">69</td><td class="ctr2">89</td><td class="ctr1">240</td><td class="ctr2">301</td><td class="ctr1">48</td><td class="ctr2">59</td><td class="ctr1">5</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a0"><a href="AsyncConnectionManager.java.html" class="el_source">AsyncConnectionManager.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="351" alt="351"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="20" alt="20"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">33</td><td class="ctr2" id="g0">33</td><td class="ctr1" id="h0">83</td><td class="ctr2" id="i0">83</td><td class="ctr1" id="j0">23</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="AsyncConnectionPool.java.html" class="el_source">AsyncConnectionPool.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="254" alt="254"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">15</td><td class="ctr2" id="g2">15</td><td class="ctr1" id="h1">62</td><td class="ctr2" id="i2">62</td><td class="ctr1" id="j1">11</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a3"><a href="ConnectionPool.java.html" class="el_source">ConnectionPool.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="238" alt="238"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">1%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">13</td><td class="ctr2" id="g3">14</td><td class="ctr1" id="h2">57</td><td class="ctr2" id="i3">58</td><td class="ctr1" id="j2">9</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a4"><a href="GrpcChannelFactory.java.html" class="el_source">GrpcChannelFactory.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="91" alt="91"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h3">33</td><td class="ctr2" id="i4">33</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k4">4</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a2"><a href="ConnectionManager.java.html" class="el_source">ConnectionManager.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="231" alt="231"/></td><td class="ctr2" id="c0">90%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="109" height="10" title="20" alt="20"/></td><td class="ctr2" id="e0">90%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g1">22</td><td class="ctr1" id="h4">5</td><td class="ctr2" id="i1">65</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>