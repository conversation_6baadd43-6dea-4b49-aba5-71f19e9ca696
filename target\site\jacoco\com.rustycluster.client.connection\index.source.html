<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.rustycluster.client.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">com.rustycluster.client.connection</span></div><h1>com.rustycluster.client.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,460 of 2,066</td><td class="ctr2">29%</td><td class="bar">76 of 126</td><td class="ctr2">39%</td><td class="ctr1">119</td><td class="ctr2">163</td><td class="ctr1">370</td><td class="ctr2">522</td><td class="ctr1">75</td><td class="ctr2">98</td><td class="ctr1">7</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a2"><a href="AsyncFailbackManager.java.html" class="el_source">AsyncFailbackManager.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="428" alt="428"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="24" alt="24"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">38</td><td class="ctr2" id="g0">38</td><td class="ctr1" id="h0">107</td><td class="ctr2" id="i0">107</td><td class="ctr1" id="j0">26</td><td class="ctr2" id="k0">26</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a0"><a href="AsyncConnectionManager.java.html" class="el_source">AsyncConnectionManager.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="114" height="10" title="407" alt="407"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="24" alt="24"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">37</td><td class="ctr2" id="g1">37</td><td class="ctr1" id="h1">92</td><td class="ctr2" id="i1">92</td><td class="ctr1" id="j1">24</td><td class="ctr2" id="k1">24</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="AsyncConnectionPool.java.html" class="el_source">AsyncConnectionPool.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="246" alt="246"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">15</td><td class="ctr2" id="g4">15</td><td class="ctr1" id="h2">61</td><td class="ctr2" id="i4">61</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k3">11</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a4"><a href="ConnectionPool.java.html" class="el_source">ConnectionPool.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="230" alt="230"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">1%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">13</td><td class="ctr2" id="g5">14</td><td class="ctr1" id="h3">57</td><td class="ctr2" id="i5">58</td><td class="ctr1" id="j3">9</td><td class="ctr2" id="k4">10</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m2">2</td></tr><tr><td id="a6"><a href="GrpcChannelFactory.java.html" class="el_source">GrpcChannelFactory.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="91" alt="91"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h4">33</td><td class="ctr2" id="i6">33</td><td class="ctr1" id="j4">4</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a5"><a href="FailbackManager.java.html" class="el_source">FailbackManager.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="301" alt="301"/></td><td class="ctr2" id="c1">90%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="95" height="10" title="27" alt="27"/></td><td class="ctr2" id="e1">79%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g2">27</td><td class="ctr1" id="h5">14</td><td class="ctr2" id="i2">91</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">10</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="ConnectionManager.java.html" class="el_source">ConnectionManager.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="280" alt="280"/></td><td class="ctr2" id="c2">90%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="81" height="10" title="23" alt="23"/></td><td class="ctr2" id="e0">88%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g3">26</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i3">76</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k2">12</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="OperationType.java.html" class="el_source">OperationType.java</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>