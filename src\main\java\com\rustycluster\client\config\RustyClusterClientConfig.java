package com.rustycluster.client.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for the RustyCluster client.
 */
public class RustyClusterClientConfig {
    private List<NodeConfig> nodes = new ArrayList<>();
    // High-throughput optimized defaults
    private int maxConnectionsPerNode = 20; // Increased from 10 for better concurrency
    private long connectionTimeoutMs = 3000; // Reduced from 5000 for faster failover
    private long readTimeoutMs = 2000; // Reduced from 5000 for faster response detection
    private long writeTimeoutMs = 2000; // Reduced from 5000 for faster write detection
    private int maxRetries = 2; // Reduced from 3 for faster failure detection
    private long retryDelayMs = 100; // Reduced from 500 for faster retry cycles
    private boolean useSecureConnection = false;
    private String tlsCertPath = null;
    private String username = null;
    private String password = null;

    /**
     * Builder for RustyClusterClientConfig.
     */
    public static class Builder {
        private final RustyClusterClientConfig config = new RustyClusterClientConfig();

        /**
         * Add a node to the configuration with a specific role.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @param role The role of the node
         * @return The builder instance
         */
        public Builder addNode(String host, int port, NodeRole role) {
            config.nodes.add(new NodeConfig(host, port, role));
            return this;
        }

        /**
         * Add a primary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addPrimaryNode(String host, int port) {
            return addNode(host, port, NodeRole.PRIMARY);
        }

        /**
         * Add a secondary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addSecondaryNode(String host, int port) {
            return addNode(host, port, NodeRole.SECONDARY);
        }

        /**
         * Add a tertiary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addTertiaryNode(String host, int port) {
            return addNode(host, port, NodeRole.TERTIARY);
        }

        /**
         * Add multiple nodes with the same role.
         *
         * @param role The role for all nodes
         * @param hostPorts Array of host:port strings (e.g., "localhost:50051")
         * @return The builder instance
         */
        public Builder addNodes(NodeRole role, String... hostPorts) {
            for (String hostPort : hostPorts) {
                String[] parts = hostPort.split(":");
                if (parts.length != 2) {
                    throw new IllegalArgumentException("Invalid host:port format: " + hostPort);
                }
                String host = parts[0];
                int port;
                try {
                    port = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid port number in: " + hostPort, e);
                }
                addNode(host, port, role);
            }
            return this;
        }

        /**
         * Add multiple primary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., "localhost:50051")
         * @return The builder instance
         */
        public Builder addPrimaryNodes(String... hostPorts) {
            return addNodes(NodeRole.PRIMARY, hostPorts);
        }

        /**
         * Add multiple secondary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., "localhost:50052")
         * @return The builder instance
         */
        public Builder addSecondaryNodes(String... hostPorts) {
            return addNodes(NodeRole.SECONDARY, hostPorts);
        }

        /**
         * Add multiple tertiary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., "localhost:50053")
         * @return The builder instance
         */
        public Builder addTertiaryNodes(String... hostPorts) {
            return addNodes(NodeRole.TERTIARY, hostPorts);
        }

        /**
         * Add nodes with automatic role assignment based on order:
         * - First node: PRIMARY
         * - Second node: SECONDARY
         * - Third and subsequent nodes: TERTIARY
         *
         * @param hostPorts Array of host:port strings (e.g., "localhost:50051")
         * @return The builder instance
         */
        public Builder addNodes(String... hostPorts) {
            if (hostPorts == null || hostPorts.length == 0) {
                return this;
            }

            // Count existing nodes to determine roles for new nodes
            int existingNodeCount = config.nodes.size();

            for (int i = 0; i < hostPorts.length; i++) {
                String hostPort = hostPorts[i];
                String[] parts = hostPort.split(":");
                if (parts.length != 2) {
                    throw new IllegalArgumentException("Invalid host:port format: " + hostPort);
                }

                String host = parts[0];
                int port;
                try {
                    port = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid port number in: " + hostPort, e);
                }

                // Determine role based on position
                NodeRole role;
                int position = existingNodeCount + i;

                if (position == 0) {
                    role = NodeRole.PRIMARY;
                } else if (position == 1) {
                    role = NodeRole.SECONDARY;
                } else {
                    role = NodeRole.TERTIARY;
                }

                addNode(host, port, role);
            }

            return this;
        }

        /**
         * Set the maximum number of connections per node.
         *
         * @param maxConnections The maximum number of connections
         * @return The builder instance
         */
        public Builder maxConnectionsPerNode(int maxConnections) {
            config.maxConnectionsPerNode = maxConnections;
            return this;
        }

        /**
         * Set the connection timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder connectionTimeout(long timeout, TimeUnit unit) {
            config.connectionTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the read timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder readTimeout(long timeout, TimeUnit unit) {
            config.readTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the write timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder writeTimeout(long timeout, TimeUnit unit) {
            config.writeTimeoutMs = unit.toMillis(timeout);
            return this;
        }

        /**
         * Set the maximum number of retries.
         *
         * @param maxRetries The maximum number of retries
         * @return The builder instance
         */
        public Builder maxRetries(int maxRetries) {
            config.maxRetries = maxRetries;
            return this;
        }

        /**
         * Set the retry delay.
         *
         * @param delay The delay value
         * @param unit  The time unit
         * @return The builder instance
         */
        public Builder retryDelay(long delay, TimeUnit unit) {
            config.retryDelayMs = unit.toMillis(delay);
            return this;
        }

        /**
         * Enable secure connection using TLS.
         *
         * @param tlsCertPath Path to the TLS certificate
         * @return The builder instance
         */
        public Builder useSecureConnection(String tlsCertPath) {
            config.useSecureConnection = true;
            config.tlsCertPath = tlsCertPath;
            return this;
        }

        /**
         * Set authentication credentials.
         *
         * @param username The username for authentication
         * @param password The password for authentication
         * @return The builder instance
         */
        public Builder authentication(String username, String password) {
            config.username = username;
            config.password = password;
            return this;
        }

        /**
         * Apply high-throughput optimized settings.
         * This preset is optimized for maximum performance in high-load scenarios.
         *
         * @return The builder instance
         */
        public Builder highThroughputPreset() {
            config.maxConnectionsPerNode = 50;
            config.connectionTimeoutMs = 1000;
            config.readTimeoutMs = 1000;
            config.writeTimeoutMs = 1000;
            config.maxRetries = 1;
            config.retryDelayMs = 50;
            return this;
        }

        /**
         * Apply low-latency optimized settings.
         * This preset is optimized for minimal latency at the cost of some throughput.
         *
         * @return The builder instance
         */
        public Builder lowLatencyPreset() {
            config.maxConnectionsPerNode = 10;
            config.connectionTimeoutMs = 500;
            config.readTimeoutMs = 500;
            config.writeTimeoutMs = 500;
            config.maxRetries = 0;
            config.retryDelayMs = 0;
            return this;
        }

        /**
         * Apply balanced settings for general use.
         * This preset balances throughput, latency, and reliability.
         *
         * @return The builder instance
         */
        public Builder balancedPreset() {
            config.maxConnectionsPerNode = 20;
            config.connectionTimeoutMs = 3000;
            config.readTimeoutMs = 2000;
            config.writeTimeoutMs = 2000;
            config.maxRetries = 2;
            config.retryDelayMs = 100;
            return this;
        }

        /**
         * Build the configuration.
         *
         * @return The built configuration
         */
        public RustyClusterClientConfig build() {
            if (config.nodes.isEmpty()) {
                throw new IllegalStateException("At least one node must be configured");
            }
            return config;
        }
    }

    /**
     * Create a new builder for RustyClusterClientConfig.
     *
     * @return A new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    // Getters
    public List<NodeConfig> getNodes() {
        return Collections.unmodifiableList(nodes);
    }

    public int getMaxConnectionsPerNode() {
        return maxConnectionsPerNode;
    }

    public long getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public long getReadTimeoutMs() {
        return readTimeoutMs;
    }

    public long getWriteTimeoutMs() {
        return writeTimeoutMs;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public long getRetryDelayMs() {
        return retryDelayMs;
    }

    public boolean isUseSecureConnection() {
        return useSecureConnection;
    }

    public String getTlsCertPath() {
        return tlsCertPath;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public boolean hasAuthentication() {
        return username != null && password != null;
    }
}
