<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.rustycluster.client.RustyClusterClientTest" time="0.423" tests="10" errors="9" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\test-classes;c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\classes;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.59.0\grpc-netty-shaded-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.0.1-android\guava-32.0.1-android.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.59.0\grpc-core-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.59.0\grpc-context-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.59.0\grpc-protobuf-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.59.0\grpc-api-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.59.0\grpc-protobuf-lite-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.59.0\grpc-stub-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.0\protobuf-java-3.24.0.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.4.0\mockito-core-5.4.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.4.0\mockito-junit-jupiter-5.4.0.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-testing\1.59.0\grpc-testing-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.59.0\grpc-inprocess-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.59.0\grpc-util-1.59.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Calcutta"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire10369071715497542983\surefirebooter-20250602144150194_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire10369071715497542983 2025-06-02T14-41-49_905-jvmRun1 surefire-20250602144150194_1tmp surefire_0-20250602144150194_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\test-classes;c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\classes;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.59.0\grpc-netty-shaded-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.0.1-android\guava-32.0.1-android.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.59.0\grpc-core-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.59.0\grpc-context-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.59.0\grpc-protobuf-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.59.0\grpc-api-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.59.0\grpc-protobuf-lite-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.59.0\grpc-stub-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.0\protobuf-java-3.24.0.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.4.0\mockito-core-5.4.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.4.0\mockito-junit-jupiter-5.4.0.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-testing\1.59.0\grpc-testing-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.59.0\grpc-inprocess-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.59.0\grpc-util-1.59.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire10369071715497542983\surefirebooter-20250602144150194_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.12+8-LTS-286"/>
    <property name="user.name" value="localadmin"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.12"/>
    <property name="user.dir" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Java\jdk-17\\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\Desktop\protoc-30.2-win64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Documents\apache-maven-3.9.9\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.12+8-LTS-286"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="shouldSetKeyValuePairWithSkipReplication" classname="com.rustycluster.client.RustyClusterClientTest" time="0.07">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:70)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePairWithSkipReplication(RustyClusterClientTest.java:75)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.497 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.500 [main] DEBUG c.r.client.RustyClusterClient - Setting key: key
]]></system-out>
  </testcase>
  <testcase name="shouldCloseConnectionManagerWhenClosed" classname="com.rustycluster.client.RustyClusterClientTest" time="0.0">
    <system-out><![CDATA[14:41:56.513 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.513 [main] DEBUG c.r.c.auth.AuthenticationManager - Authentication state cleared
14:41:56.513 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient closed
]]></system-out>
  </testcase>
  <testcase name="shouldSetKeyWithExpiration" classname="com.rustycluster.client.RustyClusterClientTest" time="0.016">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$SetExResponse.getSuccess()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetExResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.setEx(RustyClusterClient.java:156)
	at com.rustycluster.client.RustyClusterClient.setEx(RustyClusterClient.java:168)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyWithExpiration(RustyClusterClientTest.java:145)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.525 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.532 [main] DEBUG c.r.client.RustyClusterClient - Setting key with expiry: key, ttl: 60
]]></system-out>
  </testcase>
  <testcase name="shouldDeleteKey" classname="com.rustycluster.client.RustyClusterClientTest" time="0.015">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$DeleteResponse.getSuccess()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$DeleteResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.delete(RustyClusterClient.java:122)
	at com.rustycluster.client.RustyClusterClient.delete(RustyClusterClient.java:132)
	at com.rustycluster.client.RustyClusterClientTest.shouldDeleteKey(RustyClusterClientTest.java:128)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.541 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.549 [main] DEBUG c.r.client.RustyClusterClient - Deleting key: key
]]></system-out>
  </testcase>
  <testcase name="shouldGetAllHashFields" classname="com.rustycluster.client.RustyClusterClientTest" time="0.252">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$HGetAllResponse.getFieldsMap()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$HGetAllResponse.getFieldsMap()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.hGetAll(RustyClusterClient.java:374)
	at com.rustycluster.client.RustyClusterClientTest.shouldGetAllHashFields(RustyClusterClientTest.java:185)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.559 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.801 [main] DEBUG c.r.client.RustyClusterClient - Getting all hash fields for key: user:1
]]></system-out>
  </testcase>
  <testcase name="shouldSetKeyValuePair" classname="com.rustycluster.client.RustyClusterClientTest" time="0.006">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:70)
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:84)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePair(RustyClusterClientTest.java:57)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.810 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.810 [main] DEBUG c.r.client.RustyClusterClient - Setting key: key
]]></system-out>
  </testcase>
  <testcase name="shouldIncrementNumericValue" classname="com.rustycluster.client.RustyClusterClientTest" time="0.015">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$IncrByResponse.getNewValue()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$IncrByResponse.getNewValue()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.incrBy(RustyClusterClient.java:223)
	at com.rustycluster.client.RustyClusterClient.incrBy(RustyClusterClient.java:234)
	at com.rustycluster.client.RustyClusterClientTest.shouldIncrementNumericValue(RustyClusterClientTest.java:164)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.816 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.821 [main] DEBUG c.r.client.RustyClusterClient - Incrementing key: counter by 1
]]></system-out>
  </testcase>
  <testcase name="shouldGetValueByKey" classname="com.rustycluster.client.RustyClusterClientTest" time="0.008">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.get(RustyClusterClient.java:102)
	at com.rustycluster.client.RustyClusterClientTest.shouldGetValueByKey(RustyClusterClientTest.java:96)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.831 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.832 [main] DEBUG c.r.client.RustyClusterClient - Getting key: key
]]></system-out>
  </testcase>
  <testcase name="shouldExecuteBatchOperations" classname="com.rustycluster.client.RustyClusterClientTest" time="0.018">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$BatchWriteResponse.getOperationResultsList()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$BatchWriteResponse.getOperationResultsList()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.batchWrite(RustyClusterClient.java:502)
	at com.rustycluster.client.RustyClusterClient.batchWrite(RustyClusterClient.java:512)
	at com.rustycluster.client.RustyClusterClientTest.shouldExecuteBatchOperations(RustyClusterClientTest.java:210)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.839 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.845 [main] DEBUG c.r.client.RustyClusterClient - Executing batch write with 3 operations
]]></system-out>
  </testcase>
  <testcase name="shouldReturnNullWhenKeyNotFound" classname="com.rustycluster.client.RustyClusterClientTest" time="0.006">
    <error message="Cannot invoke &quot;com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()&quot; because &quot;response&quot; is null" type="java.lang.NullPointerException"><![CDATA[java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.get(RustyClusterClient.java:102)
	at com.rustycluster.client.RustyClusterClientTest.shouldReturnNullWhenKeyNotFound(RustyClusterClientTest.java:113)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
]]></error>
    <system-out><![CDATA[14:41:56.859 [main] INFO  c.r.client.RustyClusterClient - RustyClusterClient initialized
14:41:56.861 [main] DEBUG c.r.client.RustyClusterClient - Getting key: key
]]></system-out>
  </testcase>
</testsuite>