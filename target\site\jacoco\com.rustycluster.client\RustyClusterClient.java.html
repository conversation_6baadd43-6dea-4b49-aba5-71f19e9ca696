<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client</a> &gt; <span class="el_source">RustyClusterClient.java</span></div><h1>RustyClusterClient.java</h1><pre class="source lang-java linenums">package com.rustycluster.client;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.connection.ConnectionManager;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Client for interacting with RustyCluster.
 */
public class RustyClusterClient implements AutoCloseable {
<span class="fc" id="L17">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L28">    public RustyClusterClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L29">        this.config = config;</span>
<span class="nc" id="L30">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L31">        this.connectionManager = new ConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L32">        logger.info(&quot;RustyClusterClient initialized&quot;);</span>
<span class="nc" id="L33">    }</span>

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
<span class="fc" id="L41">    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {</span>
<span class="fc" id="L42">        this.config = config;</span>
<span class="fc" id="L43">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L44">        this.connectionManager = connectionManager;</span>
<span class="fc" id="L45">        logger.info(&quot;RustyClusterClient initialized&quot;);</span>
<span class="fc" id="L46">    }</span>

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
<span class="fc" id="L57">        logger.debug(&quot;Setting key: {}&quot;, key);</span>

        try {
<span class="fc" id="L60">            RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="fc" id="L61">                    .setKey(key)</span>
<span class="fc" id="L62">                    .setValue(value)</span>
<span class="fc" id="L63">                    .setSkipReplication(skipReplication)</span>
<span class="fc" id="L64">                    .build();</span>

<span class="fc" id="L66">            RustyClusterProto.SetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L67">                    stub.set(request));</span>

<span class="fc" id="L69">            return response.getSuccess();</span>
<span class="nc" id="L70">        } catch (Exception e) {</span>
<span class="nc" id="L71">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="fc" id="L83">        return set(key, value, false);</span>
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found
     */
    public String get(String key) {
<span class="fc" id="L93">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
<span class="fc" id="L94">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L95">                .setKey(key)</span>
<span class="fc" id="L96">                .build();</span>

<span class="fc" id="L98">        RustyClusterProto.GetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L99">                stub.get(request));</span>

<span class="fc bfc" id="L101" title="All 2 branches covered.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
<span class="fc" id="L112">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
<span class="fc" id="L113">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="fc" id="L114">                .setKey(key)</span>
<span class="fc" id="L115">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L116">                .build();</span>

<span class="fc" id="L118">        RustyClusterProto.DeleteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L119">                stub.delete(request));</span>

<span class="fc" id="L121">        return response.getSuccess();</span>
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="fc" id="L131">        return delete(key, false);</span>
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
<span class="fc" id="L144">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttl);</span>
<span class="fc" id="L145">        RustyClusterProto.SetExRequest request = RustyClusterProto.SetExRequest.newBuilder()</span>
<span class="fc" id="L146">                .setKey(key)</span>
<span class="fc" id="L147">                .setValue(value)</span>
<span class="fc" id="L148">                .setTtl(ttl)</span>
<span class="fc" id="L149">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L150">                .build();</span>

<span class="fc" id="L152">        RustyClusterProto.SetExResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L153">                stub.setEx(request));</span>

<span class="fc" id="L155">        return response.getSuccess();</span>
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
<span class="fc" id="L167">        return setEx(key, value, ttl, false);</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
<span class="nc" id="L179">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttl);</span>
<span class="nc" id="L180">        RustyClusterProto.SetExpiryRequest request = RustyClusterProto.SetExpiryRequest.newBuilder()</span>
<span class="nc" id="L181">                .setKey(key)</span>
<span class="nc" id="L182">                .setTtl(ttl)</span>
<span class="nc" id="L183">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L184">                .build();</span>

<span class="nc" id="L186">        RustyClusterProto.SetExpiryResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L187">                stub.setExpiry(request));</span>

<span class="nc" id="L189">        return response.getSuccess();</span>
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
<span class="nc" id="L200">        return setExpiry(key, ttl, false);</span>
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
<span class="fc" id="L212">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
<span class="fc" id="L213">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="fc" id="L214">                .setKey(key)</span>
<span class="fc" id="L215">                .setValue(value)</span>
<span class="fc" id="L216">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L217">                .build();</span>

<span class="fc" id="L219">        RustyClusterProto.IncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L220">                stub.incrBy(request));</span>

<span class="fc" id="L222">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="fc" id="L233">        return incrBy(key, value, false);</span>
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
<span class="nc" id="L245">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
<span class="nc" id="L246">        RustyClusterProto.DecrByRequest request = RustyClusterProto.DecrByRequest.newBuilder()</span>
<span class="nc" id="L247">                .setKey(key)</span>
<span class="nc" id="L248">                .setValue(value)</span>
<span class="nc" id="L249">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L250">                .build();</span>

<span class="nc" id="L252">        RustyClusterProto.DecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L253">                stub.decrBy(request));</span>

<span class="nc" id="L255">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L266">        return decrBy(key, value, false);</span>
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
<span class="nc" id="L278">        logger.debug(&quot;Incrementing key: {} by float {}&quot;, key, value);</span>
<span class="nc" id="L279">        RustyClusterProto.IncrByFloatRequest request = RustyClusterProto.IncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L280">                .setKey(key)</span>
<span class="nc" id="L281">                .setValue(value)</span>
<span class="nc" id="L282">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L283">                .build();</span>

<span class="nc" id="L285">        RustyClusterProto.IncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L286">                stub.incrByFloat(request));</span>

<span class="nc" id="L288">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
<span class="nc" id="L299">        return incrByFloat(key, value, false);</span>
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L312">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L313">        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()</span>
<span class="nc" id="L314">                .setKey(key)</span>
<span class="nc" id="L315">                .setField(field)</span>
<span class="nc" id="L316">                .setValue(value)</span>
<span class="nc" id="L317">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L318">                .build();</span>

<span class="nc" id="L320">        RustyClusterProto.HSetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L321">                stub.hSet(request));</span>

<span class="nc" id="L323">        return response.getSuccess();</span>
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L335">        return hSet(key, field, value, false);</span>
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found
     */
    public String hGet(String key, String field) {
<span class="nc" id="L346">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L347">        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()</span>
<span class="nc" id="L348">                .setKey(key)</span>
<span class="nc" id="L349">                .setField(field)</span>
<span class="nc" id="L350">                .build();</span>

<span class="nc" id="L352">        RustyClusterProto.HGetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L353">                stub.hGet(request));</span>

<span class="nc bnc" id="L355" title="All 2 branches missed.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="fc" id="L365">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
<span class="fc" id="L366">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="fc" id="L367">                .setKey(key)</span>
<span class="fc" id="L368">                .build();</span>

<span class="fc" id="L370">        RustyClusterProto.HGetAllResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L371">                stub.hGetAll(request));</span>

<span class="fc" id="L373">        return response.getFieldsMap();</span>
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L386">        logger.debug(&quot;Incrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L387">        RustyClusterProto.HIncrByRequest request = RustyClusterProto.HIncrByRequest.newBuilder()</span>
<span class="nc" id="L388">                .setKey(key)</span>
<span class="nc" id="L389">                .setField(field)</span>
<span class="nc" id="L390">                .setValue(value)</span>
<span class="nc" id="L391">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L392">                .build();</span>

<span class="nc" id="L394">        RustyClusterProto.HIncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L395">                stub.hIncrBy(request));</span>

<span class="nc" id="L397">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
<span class="nc" id="L409">        return hIncrBy(key, field, value, false);</span>
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L422">        logger.debug(&quot;Decrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L423">        RustyClusterProto.HDecrByRequest request = RustyClusterProto.HDecrByRequest.newBuilder()</span>
<span class="nc" id="L424">                .setKey(key)</span>
<span class="nc" id="L425">                .setField(field)</span>
<span class="nc" id="L426">                .setValue(value)</span>
<span class="nc" id="L427">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L428">                .build();</span>

<span class="nc" id="L430">        RustyClusterProto.HDecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L431">                stub.hDecrBy(request));</span>

<span class="nc" id="L433">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
<span class="nc" id="L445">        return hDecrBy(key, field, value, false);</span>
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
<span class="nc" id="L458">        logger.debug(&quot;Incrementing hash field: {}.{} by float {}&quot;, key, field, value);</span>
<span class="nc" id="L459">        RustyClusterProto.HIncrByFloatRequest request = RustyClusterProto.HIncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L460">                .setKey(key)</span>
<span class="nc" id="L461">                .setField(field)</span>
<span class="nc" id="L462">                .setValue(value)</span>
<span class="nc" id="L463">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L464">                .build();</span>

<span class="nc" id="L466">        RustyClusterProto.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L467">                stub.hIncrByFloat(request));</span>

<span class="nc" id="L469">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
<span class="nc" id="L481">        return hIncrByFloat(key, field, value, false);</span>
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="fc" id="L492">        logger.debug(&quot;Executing batch write with {} operations&quot;, operations.size());</span>
<span class="fc" id="L493">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="fc" id="L494">                .addAllOperations(operations)</span>
<span class="fc" id="L495">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L496">                .build();</span>

<span class="fc" id="L498">        RustyClusterProto.BatchWriteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L499">                stub.batchWrite(request));</span>

<span class="fc" id="L501">        return response.getOperationResultsList();</span>
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="fc" id="L511">        return batchWrite(operations, false);</span>
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
<span class="nc" id="L521">        logger.debug(&quot;Attempting to authenticate with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L523" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L524">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L525">            return true;</span>
        }

        try {
            // Get a stub from the connection manager and authenticate
<span class="nc" id="L530">            return connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L531">                authenticationManager.authenticate(stub));</span>
<span class="nc" id="L532">        } catch (Exception e) {</span>
<span class="nc" id="L533">            logger.error(&quot;Authentication failed&quot;, e);</span>
<span class="nc" id="L534">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L544">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L553">        return authenticationManager.getSessionToken();</span>
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L561">        authenticationManager.clearAuthentication();</span>
<span class="fc" id="L562">        connectionManager.close();</span>
<span class="fc" id="L563">        logger.info(&quot;RustyClusterClient closed&quot;);</span>
<span class="fc" id="L564">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>