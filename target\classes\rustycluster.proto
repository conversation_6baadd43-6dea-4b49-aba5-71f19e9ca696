syntax = "proto3";

package rustycluster;

option java_package = "com.rustycluster.grpc";
option java_outer_classname = "RustyClusterProto";

service KeyValueService {
  // Authentication operations
  rpc Authenticate (AuthenticateRequest) returns (AuthenticateResponse);

  // System operations
  rpc Ping (PingRequest) returns (PingResponse);

  // String operations
  rpc Set (SetRequest) returns (SetResponse);
  rpc Get (GetRequest) returns (GetResponse);
  rpc Delete (DeleteRequest) returns (DeleteResponse);
  rpc SetEx (SetExRequest) returns (SetExResponse);
  rpc SetExpiry (SetExpiryRequest) returns (SetExpiryResponse);

  // Numeric operations
  rpc IncrBy (IncrByRequest) returns (IncrByResponse);
  rpc DecrBy (DecrByRequest) returns (DecrByResponse);
  rpc IncrByFloat (IncrByFloatRequest) returns (IncrByFloatResponse);

  // Hash operations
  rpc HSet (HSetRequest) returns (HSetResponse);
  rpc HGet (HGetRequest) returns (HGetResponse);
  rpc HGetAll (HGetAllRequest) returns (HGetAllResponse);
  rpc HIncrBy (HIncrByRequest) returns (HIncrByResponse);
  rpc HDecrBy (HDecrByRequest) returns (HDecrByResponse);
  rpc HIncrByFloat (HIncrByFloatRequest) returns (HIncrByFloatResponse);

  // Batch operations
  rpc BatchWrite (BatchWriteRequest) returns (BatchWriteResponse);
}

// Authentication operations
message AuthenticateRequest {
  string username = 1;
  string password = 2;
}

message AuthenticateResponse {
  bool success = 1;
  string session_token = 2;
  string message = 3;
}

// String operations
message SetRequest {
  string key = 1;
  string value = 2;
  bool skip_replication = 3;
}

message SetResponse {
  bool success = 1;
}

message GetRequest {
  string key = 1;
}

message GetResponse {
  string value = 1;
  bool found = 2;
}

message DeleteRequest {
  string key = 1;
  bool skip_replication = 2;
}

message DeleteResponse {
  bool success = 1;
}

message SetExRequest {
  string key = 1;
  string value = 2;
  uint64 ttl = 3;
  bool skip_replication = 4;
}

message SetExResponse {
  bool success = 1;
}

message SetExpiryRequest {
  string key = 1;
  int64 ttl = 2;
  bool skip_replication = 3;
}

message SetExpiryResponse {
  bool success = 1;
}

// Numeric operations
message IncrByRequest {
  string key = 1;
  int64 value = 2;
  bool skip_replication = 3;
}

message IncrByResponse {
  int64 new_value = 1;
}

message DecrByRequest {
  string key = 1;
  int64 value = 2;
  bool skip_replication = 3;
}

message DecrByResponse {
  int64 new_value = 1;
}

message IncrByFloatRequest {
  string key = 1;
  double value = 2;
  bool skip_replication = 3;
}

message IncrByFloatResponse {
  double new_value = 1;
}

// Hash operations
message HSetRequest {
  string key = 1;
  string field = 2;
  string value = 3;
  bool skip_replication = 4;
}

message HSetResponse {
  bool success = 1;
}

message HGetRequest {
  string key = 1;
  string field = 2;
}

message HGetResponse {
  string value = 1;
  bool found = 2;
}

message HGetAllRequest {
  string key = 1;
}

message HGetAllResponse {
  map<string, string> fields = 1;
}

message HIncrByRequest {
  string key = 1;
  string field = 2;
  int64 value = 3;
  bool skip_replication = 4;
}

message HIncrByResponse {
  int64 new_value = 1;
}

message HDecrByRequest {
  string key = 1;
  string field = 2;
  int64 value = 3;
  bool skip_replication = 4;
}

message HDecrByResponse {
  int64 new_value = 1;
}

message HIncrByFloatRequest {
  string key = 1;
  string field = 2;
  double value = 3;
  bool skip_replication = 4;
}

message HIncrByFloatResponse {
  double new_value = 1;
}

// Batch operations
message BatchOperation {
  enum OperationType {
    SET = 0;
    DELETE = 1;
    SETEX = 2;
    SETEXPIRY = 3;
    INCRBY = 4;
    DECRBY = 5;
    INCRBYFLOAT = 6;
    HSET = 7;
    HINCRBY = 8;
    HDECRBY = 9;
    HINCRBYFLOAT = 10;
  }

  OperationType operation_type = 1;
  string key = 2;
  string value = 3;
  optional string field = 4;  // For hash operations
  optional int64 int_value = 5;  // For numeric operations
  optional double float_value = 6;  // For float operations
  optional uint64 ttl = 7;  // For expiry operations
}

message BatchWriteRequest {
  repeated BatchOperation operations = 1;
  bool skip_replication = 2;
}

message BatchWriteResponse {
  bool success = 1;
  repeated bool operation_results = 2;  // Results for each operation in the batch
}

// System operations
message PingRequest {
  // Empty request
}

message PingResponse {
  bool success = 1;
  string message = 2;
  int64 latency_ms = 3;  // Latency in milliseconds
}