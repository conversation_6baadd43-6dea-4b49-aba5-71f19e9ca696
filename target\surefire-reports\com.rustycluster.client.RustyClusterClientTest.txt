-------------------------------------------------------------------------------
Test set: com.rustycluster.client.RustyClusterClientTest
-------------------------------------------------------------------------------
Tests run: 10, Failures: 0, Errors: 9, Skipped: 0, Time elapsed: 0.423 s <<< FAILURE! -- in com.rustycluster.client.RustyClusterClientTest
com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePairWithSkipReplication -- Time elapsed: 0.070 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:70)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePairWithSkipReplication(RustyClusterClientTest.java:75)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldSetKeyWithExpiration -- Time elapsed: 0.016 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetExResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.setEx(RustyClusterClient.java:156)
	at com.rustycluster.client.RustyClusterClient.setEx(RustyClusterClient.java:168)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyWithExpiration(RustyClusterClientTest.java:145)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldDeleteKey -- Time elapsed: 0.015 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$DeleteResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.delete(RustyClusterClient.java:122)
	at com.rustycluster.client.RustyClusterClient.delete(RustyClusterClient.java:132)
	at com.rustycluster.client.RustyClusterClientTest.shouldDeleteKey(RustyClusterClientTest.java:128)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldGetAllHashFields -- Time elapsed: 0.252 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$HGetAllResponse.getFieldsMap()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.hGetAll(RustyClusterClient.java:374)
	at com.rustycluster.client.RustyClusterClientTest.shouldGetAllHashFields(RustyClusterClientTest.java:185)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePair -- Time elapsed: 0.006 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$SetResponse.getSuccess()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:70)
	at com.rustycluster.client.RustyClusterClient.set(RustyClusterClient.java:84)
	at com.rustycluster.client.RustyClusterClientTest.shouldSetKeyValuePair(RustyClusterClientTest.java:57)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldIncrementNumericValue -- Time elapsed: 0.015 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$IncrByResponse.getNewValue()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.incrBy(RustyClusterClient.java:223)
	at com.rustycluster.client.RustyClusterClient.incrBy(RustyClusterClient.java:234)
	at com.rustycluster.client.RustyClusterClientTest.shouldIncrementNumericValue(RustyClusterClientTest.java:164)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldGetValueByKey -- Time elapsed: 0.008 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.get(RustyClusterClient.java:102)
	at com.rustycluster.client.RustyClusterClientTest.shouldGetValueByKey(RustyClusterClientTest.java:96)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldExecuteBatchOperations -- Time elapsed: 0.018 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$BatchWriteResponse.getOperationResultsList()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.batchWrite(RustyClusterClient.java:502)
	at com.rustycluster.client.RustyClusterClient.batchWrite(RustyClusterClient.java:512)
	at com.rustycluster.client.RustyClusterClientTest.shouldExecuteBatchOperations(RustyClusterClientTest.java:210)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.RustyClusterClientTest.shouldReturnNullWhenKeyNotFound -- Time elapsed: 0.006 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.rustycluster.grpc.RustyClusterProto$GetResponse.getFound()" because "response" is null
	at com.rustycluster.client.RustyClusterClient.get(RustyClusterClient.java:102)
	at com.rustycluster.client.RustyClusterClientTest.shouldReturnNullWhenKeyNotFound(RustyClusterClientTest.java:113)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

