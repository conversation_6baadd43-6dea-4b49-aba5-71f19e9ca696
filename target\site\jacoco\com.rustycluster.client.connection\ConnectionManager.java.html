<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">ConnectionManager.java</span></div><h1>ConnectionManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.exception.NoAvailableNodesException;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages connections to RustyCluster nodes, handling prioritization and failover.
 */
public class ConnectionManager implements AutoCloseable {
<span class="fc" id="L19">    private static final Logger logger = LoggerFactory.getLogger(ConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;

    /**
     * Create a new ConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public ConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L33">        this(config, new ConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L34">    }</span>

    /**
     * Create a new ConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="fc" id="L42">    ConnectionManager(RustyClusterClientConfig config, ConnectionPool connectionPool) {</span>
<span class="fc" id="L43">        this.config = config;</span>
<span class="fc" id="L44">        this.connectionPool = connectionPool;</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L47">        this.sortedNodes = config.getNodes().stream()</span>
<span class="fc" id="L48">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L49">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L52">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

<span class="fc" id="L54">        logger.info(&quot;ConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L55">    }</span>

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation) throws NoAvailableNodesException {
<span class="fc" id="L66">        int retries = 0;</span>
<span class="fc" id="L67">        Exception lastException = null;</span>

<span class="fc bfc" id="L69" title="All 2 branches covered.">        while (retries &lt;= config.getMaxRetries()) {</span>
<span class="fc" id="L70">            NodeConfig node = currentNode.get();</span>
<span class="fc" id="L71">            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>

            try {
<span class="fc" id="L74">                stub = connectionPool.borrowStub(node);</span>
<span class="fc" id="L75">                return operation.execute(stub);</span>
<span class="fc" id="L76">            } catch (Exception e) {</span>
<span class="fc" id="L77">                lastException = e;</span>
<span class="fc" id="L78">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, e.getMessage());</span>

                // Try to find the next available node
<span class="fc" id="L81">                var nextNode = findNextAvailableNode(node);</span>
<span class="fc bfc" id="L82" title="All 2 branches covered.">                if (nextNode != null) {</span>
<span class="fc" id="L83">                    currentNode.set(nextNode);</span>
<span class="fc" id="L84">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>
<span class="fc" id="L85">                } else {</span>
<span class="fc" id="L86">                    logger.warn(&quot;No available nodes found after failure&quot;);</span>
                }

<span class="fc" id="L89">                retries++;</span>

<span class="fc bfc" id="L91" title="All 2 branches covered.">                if (retries &lt;= config.getMaxRetries()) {</span>
                    try {
<span class="fc" id="L93">                        Thread.sleep(config.getRetryDelayMs());</span>
<span class="pc" id="L94">                    } catch (InterruptedException ie) {</span>
<span class="nc" id="L95">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L96">                        throw new RuntimeException(&quot;Interrupted during retry delay&quot;, ie);</span>
                    }
                }
            } finally {
<span class="fc bfc" id="L100" title="All 2 branches covered.">                if (stub != null) {</span>
<span class="fc" id="L101">                    connectionPool.returnStub(node, stub);</span>
                }
            }
        }

<span class="fc" id="L106">        throw new NoAvailableNodesException(&quot;Operation failed after &quot; + retries + &quot; retries&quot;, lastException);</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="fc" id="L117">        var samePriorityNode = sortedNodes.stream()</span>
<span class="pc bpc" id="L118" title="1 of 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="fc" id="L119">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L120">                .findFirst();</span>

<span class="pc bpc" id="L122" title="1 of 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L123">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="fc" id="L127">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="fc bfc" id="L128" title="All 2 branches covered.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="fc" id="L129">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L130">                .findFirst();</span>

<span class="fc bfc" id="L132" title="All 2 branches covered.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="fc" id="L133">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="fc" id="L137">        return sortedNodes.stream()</span>
<span class="fc bfc" id="L138" title="All 2 branches covered.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="fc" id="L139">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L140">                .findFirst()</span>
<span class="fc" id="L141">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
<span class="fc" id="L151">        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>
        try {
<span class="fc" id="L153">            stub = connectionPool.borrowStub(node);</span>
            // In a real implementation, you might want to perform a health check
<span class="fc" id="L155">            return true;</span>
<span class="fc" id="L156">        } catch (Exception e) {</span>
<span class="fc" id="L157">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="fc" id="L158">            return false;</span>
        } finally {
<span class="fc bfc" id="L160" title="All 2 branches covered.">            if (stub != null) {</span>
<span class="fc" id="L161">                connectionPool.returnStub(node, stub);</span>
            }
        }
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L171">        connectionPool.close();</span>
<span class="fc" id="L172">        logger.info(&quot;ConnectionManager closed&quot;);</span>
<span class="fc" id="L173">    }</span>

    /**
     * Functional interface for client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface ClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return The result of the operation
         * @throws Exception If the operation fails
         */
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>