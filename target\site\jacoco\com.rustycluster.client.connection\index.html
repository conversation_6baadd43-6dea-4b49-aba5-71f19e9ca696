<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.rustycluster.client.connection</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">com.rustycluster.client.connection</span></div><h1>com.rustycluster.client.connection</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">958 of 1,193</td><td class="ctr2">19%</td><td class="bar">40 of 60</td><td class="ctr2">33%</td><td class="ctr1">69</td><td class="ctr2">89</td><td class="ctr1">240</td><td class="ctr2">301</td><td class="ctr1">48</td><td class="ctr2">59</td><td class="ctr1">5</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a0"><a href="AsyncConnectionManager.html" class="el_class">AsyncConnectionManager</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="351" alt="351"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="20" alt="20"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">33</td><td class="ctr2" id="g0">33</td><td class="ctr1" id="h0">83</td><td class="ctr2" id="i0">83</td><td class="ctr1" id="j0">23</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="AsyncConnectionPool.html" class="el_class">AsyncConnectionPool</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="62" height="10" title="184" alt="184"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g2">10</td><td class="ctr1" id="h1">45</td><td class="ctr2" id="i2">45</td><td class="ctr1" id="j1">6</td><td class="ctr2" id="k2">6</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a4"><a href="ConnectionPool.html" class="el_class">ConnectionPool</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="168" alt="168"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c1">2%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g3">9</td><td class="ctr1" id="h2">40</td><td class="ctr2" id="i3">41</td><td class="ctr1" id="j4">4</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a6"><a href="GrpcChannelFactory.html" class="el_class">GrpcChannelFactory</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="91" alt="91"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h3">33</td><td class="ctr2" id="i4">33</td><td class="ctr1" id="j5">4</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="ConnectionPool$StubFactory.html" class="el_class">ConnectionPool.StubFactory</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="70" alt="70"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h4">17</td><td class="ctr2" id="i5">17</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k4">5</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a2"><a href="AsyncConnectionPool$AsyncStubFactory.html" class="el_class">AsyncConnectionPool.AsyncStubFactory</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="70" alt="70"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h5">17</td><td class="ctr2" id="i6">17</td><td class="ctr1" id="j3">5</td><td class="ctr2" id="k5">5</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="ConnectionManager.html" class="el_class">ConnectionManager</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="231" alt="231"/></td><td class="ctr2" id="c0">90%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="109" height="10" title="20" alt="20"/></td><td class="ctr2" id="e0">90%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g1">22</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i1">65</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>