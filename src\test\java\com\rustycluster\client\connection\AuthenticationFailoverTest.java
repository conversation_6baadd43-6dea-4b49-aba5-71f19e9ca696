package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.NodeRole;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test for authentication behavior during failover scenarios.
 */
@ExtendWith(MockitoExtension.class)
class AuthenticationFailoverTest {

    @Mock
    private ConnectionPool connectionPool;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStub;

    @Mock
    private KeyValueServiceGrpc.KeyValueServiceBlockingStub mockStubWithDeadline;

    private RustyClusterClientConfig config;
    private ConnectionManager connectionManager;

    @BeforeEach
    void setUp() {
        config = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                .authentication("testuser", "testpass")
                .build();

        // Mock the connection pool to return our mock authentication manager
        when(connectionPool.getAuthenticationManager()).thenReturn(authenticationManager);

        connectionManager = new ConnectionManager(config, connectionPool);
    }

    @Test
    @DisplayName("Should clear authentication state when switching nodes")
    void shouldClearAuthenticationStateWhenSwitchingNodes() throws Exception {
        // Given - authentication manager is authenticated
        when(authenticationManager.isAuthenticated()).thenReturn(true);
        when(authenticationManager.getSessionToken()).thenReturn("valid-session-token");

        // Mock the connection pool to throw an exception for primary node (to trigger failover)
        NodeConfig primaryNode = new NodeConfig("primary", 50051, NodeRole.PRIMARY);
        NodeConfig secondaryNode = new NodeConfig("secondary", 50052, NodeRole.SECONDARY);
        
        when(connectionPool.borrowStub(primaryNode))
                .thenThrow(new RuntimeException("Connection failed"));
        when(connectionPool.borrowStub(secondaryNode))
                .thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(anyLong(), any(TimeUnit.class)))
                .thenReturn(mockStubWithDeadline);
        when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                .thenReturn(RustyClusterProto.GetResponse.newBuilder().build());

        // When - execute an operation that will trigger failover
        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()
                .setKey("test-key")
                .build();

        connectionManager.executeWithFailover(
            stub -> stub.get(request),
            OperationType.READ
        );

        // Then - authentication should have been cleared when switching nodes
        verify(authenticationManager).clearAuthentication();
        verify(connectionPool).borrowStub(primaryNode); // First attempt
        verify(connectionPool).borrowStub(secondaryNode); // Failover attempt
    }

    @Test
    @DisplayName("Should not clear authentication state when no failover occurs")
    void shouldNotClearAuthenticationStateWhenNoFailoverOccurs() throws Exception {
        // Given - authentication manager is authenticated
        when(authenticationManager.isAuthenticated()).thenReturn(true);
        when(authenticationManager.getSessionToken()).thenReturn("valid-session-token");

        // Mock successful operation on primary node
        NodeConfig primaryNode = new NodeConfig("primary", 50051, NodeRole.PRIMARY);
        
        when(connectionPool.borrowStub(primaryNode))
                .thenReturn(mockStub);
        when(mockStub.withDeadlineAfter(anyLong(), any(TimeUnit.class)))
                .thenReturn(mockStubWithDeadline);
        when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                .thenReturn(RustyClusterProto.GetResponse.newBuilder().build());

        // When - execute a successful operation
        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()
                .setKey("test-key")
                .build();

        connectionManager.executeWithFailover(
            stub -> stub.get(request),
            OperationType.READ
        );

        // Then - authentication should NOT have been cleared
        verify(authenticationManager, never()).clearAuthentication();
        verify(connectionPool).borrowStub(primaryNode); // Only one attempt
    }

    @Test
    @DisplayName("Should clear authentication state only when authentication is configured")
    void shouldClearAuthenticationStateOnlyWhenAuthenticationConfigured() throws Exception {
        // Given - config without authentication
        RustyClusterClientConfig configWithoutAuth = RustyClusterClientConfig.builder()
                .addNode("primary", 50051, NodeRole.PRIMARY)
                .addNode("secondary", 50052, NodeRole.SECONDARY)
                // No username/password
                .build();

        try (ConnectionManager connectionManagerWithoutAuth = new ConnectionManager(configWithoutAuth, connectionPool)) {
            // Mock failover scenario
            NodeConfig primaryNode = new NodeConfig("primary", 50051, NodeRole.PRIMARY);
            NodeConfig secondaryNode = new NodeConfig("secondary", 50052, NodeRole.SECONDARY);

            when(connectionPool.borrowStub(primaryNode))
                    .thenThrow(new RuntimeException("Connection failed"));
            when(connectionPool.borrowStub(secondaryNode))
                    .thenReturn(mockStub);
            when(mockStub.withDeadlineAfter(anyLong(), any(TimeUnit.class)))
                    .thenReturn(mockStubWithDeadline);
            when(mockStubWithDeadline.get(any(RustyClusterProto.GetRequest.class)))
                    .thenReturn(RustyClusterProto.GetResponse.newBuilder().build());

            // When - execute an operation that will trigger failover
            RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()
                    .setKey("test-key")
                    .build();

            connectionManagerWithoutAuth.executeWithFailover(
                stub -> stub.get(request),
                OperationType.READ
            );

            // Then - authentication should NOT have been cleared (no auth configured)
            verify(authenticationManager, never()).clearAuthentication();
        }
    }
}
