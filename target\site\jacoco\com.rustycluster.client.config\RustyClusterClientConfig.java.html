<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClientConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.config</a> &gt; <span class="el_source">RustyClusterClientConfig.java</span></div><h1>RustyClusterClientConfig.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.config;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Configuration for the RustyCluster client.
 */
<span class="fc" id="L11">public class RustyClusterClientConfig {</span>
<span class="fc" id="L12">    private List&lt;NodeConfig&gt; nodes = new ArrayList&lt;&gt;();</span>
    // High-throughput optimized defaults
<span class="fc" id="L14">    private int maxConnectionsPerNode = 20; // Increased from 10 for better concurrency</span>
<span class="fc" id="L15">    private long connectionTimeoutMs = 3000; // Reduced from 5000 for faster failover</span>
<span class="fc" id="L16">    private long readTimeoutMs = 2000; // Reduced from 5000 for faster response detection</span>
<span class="fc" id="L17">    private long writeTimeoutMs = 2000; // Reduced from 5000 for faster write detection</span>
<span class="fc" id="L18">    private int maxRetries = 2; // Reduced from 3 for faster failure detection</span>
<span class="fc" id="L19">    private long retryDelayMs = 100; // Reduced from 500 for faster retry cycles</span>
<span class="fc" id="L20">    private boolean useSecureConnection = false;</span>
<span class="fc" id="L21">    private String tlsCertPath = null;</span>
<span class="fc" id="L22">    private String username = null;</span>
<span class="fc" id="L23">    private String password = null;</span>

    /**
     * Builder for RustyClusterClientConfig.
     */
<span class="fc" id="L28">    public static class Builder {</span>
<span class="fc" id="L29">        private final RustyClusterClientConfig config = new RustyClusterClientConfig();</span>

        /**
         * Add a node to the configuration with a specific role.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @param role The role of the node
         * @return The builder instance
         */
        public Builder addNode(String host, int port, NodeRole role) {
<span class="fc" id="L40">            config.nodes.add(new NodeConfig(host, port, role));</span>
<span class="fc" id="L41">            return this;</span>
        }

        /**
         * Add a primary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addPrimaryNode(String host, int port) {
<span class="fc" id="L52">            return addNode(host, port, NodeRole.PRIMARY);</span>
        }

        /**
         * Add a secondary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addSecondaryNode(String host, int port) {
<span class="fc" id="L63">            return addNode(host, port, NodeRole.SECONDARY);</span>
        }

        /**
         * Add a tertiary node to the configuration.
         *
         * @param host The host address of the node
         * @param port The port number of the node
         * @return The builder instance
         */
        public Builder addTertiaryNode(String host, int port) {
<span class="fc" id="L74">            return addNode(host, port, NodeRole.TERTIARY);</span>
        }

        /**
         * Add multiple nodes with the same role.
         *
         * @param role The role for all nodes
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addNodes(NodeRole role, String... hostPorts) {
<span class="fc bfc" id="L85" title="All 2 branches covered.">            for (String hostPort : hostPorts) {</span>
<span class="fc" id="L86">                String[] parts = hostPort.split(&quot;:&quot;);</span>
<span class="fc bfc" id="L87" title="All 2 branches covered.">                if (parts.length != 2) {</span>
<span class="fc" id="L88">                    throw new IllegalArgumentException(&quot;Invalid host:port format: &quot; + hostPort);</span>
                }
<span class="fc" id="L90">                String host = parts[0];</span>
                int port;
                try {
<span class="fc" id="L93">                    port = Integer.parseInt(parts[1]);</span>
<span class="fc" id="L94">                } catch (NumberFormatException e) {</span>
<span class="fc" id="L95">                    throw new IllegalArgumentException(&quot;Invalid port number in: &quot; + hostPort, e);</span>
<span class="fc" id="L96">                }</span>
<span class="fc" id="L97">                addNode(host, port, role);</span>
            }
<span class="fc" id="L99">            return this;</span>
        }

        /**
         * Add multiple primary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addPrimaryNodes(String... hostPorts) {
<span class="fc" id="L109">            return addNodes(NodeRole.PRIMARY, hostPorts);</span>
        }

        /**
         * Add multiple secondary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50052&quot;)
         * @return The builder instance
         */
        public Builder addSecondaryNodes(String... hostPorts) {
<span class="fc" id="L119">            return addNodes(NodeRole.SECONDARY, hostPorts);</span>
        }

        /**
         * Add multiple tertiary nodes.
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50053&quot;)
         * @return The builder instance
         */
        public Builder addTertiaryNodes(String... hostPorts) {
<span class="fc" id="L129">            return addNodes(NodeRole.TERTIARY, hostPorts);</span>
        }

        /**
         * Add nodes with automatic role assignment based on order:
         * - First node: PRIMARY
         * - Second node: SECONDARY
         * - Third and subsequent nodes: TERTIARY
         *
         * @param hostPorts Array of host:port strings (e.g., &quot;localhost:50051&quot;)
         * @return The builder instance
         */
        public Builder addNodes(String... hostPorts) {
<span class="pc bpc" id="L142" title="2 of 4 branches missed.">            if (hostPorts == null || hostPorts.length == 0) {</span>
<span class="nc" id="L143">                return this;</span>
            }

            // Count existing nodes to determine roles for new nodes
<span class="fc" id="L147">            int existingNodeCount = config.nodes.size();</span>

<span class="fc bfc" id="L149" title="All 2 branches covered.">            for (int i = 0; i &lt; hostPorts.length; i++) {</span>
<span class="fc" id="L150">                String hostPort = hostPorts[i];</span>
<span class="fc" id="L151">                String[] parts = hostPort.split(&quot;:&quot;);</span>
<span class="pc bpc" id="L152" title="1 of 2 branches missed.">                if (parts.length != 2) {</span>
<span class="nc" id="L153">                    throw new IllegalArgumentException(&quot;Invalid host:port format: &quot; + hostPort);</span>
                }

<span class="fc" id="L156">                String host = parts[0];</span>
                int port;
                try {
<span class="fc" id="L159">                    port = Integer.parseInt(parts[1]);</span>
<span class="nc" id="L160">                } catch (NumberFormatException e) {</span>
<span class="nc" id="L161">                    throw new IllegalArgumentException(&quot;Invalid port number in: &quot; + hostPort, e);</span>
<span class="fc" id="L162">                }</span>

                // Determine role based on position
                NodeRole role;
<span class="fc" id="L166">                int position = existingNodeCount + i;</span>

<span class="fc bfc" id="L168" title="All 2 branches covered.">                if (position == 0) {</span>
<span class="fc" id="L169">                    role = NodeRole.PRIMARY;</span>
<span class="fc bfc" id="L170" title="All 2 branches covered.">                } else if (position == 1) {</span>
<span class="fc" id="L171">                    role = NodeRole.SECONDARY;</span>
                } else {
<span class="fc" id="L173">                    role = NodeRole.TERTIARY;</span>
                }

<span class="fc" id="L176">                addNode(host, port, role);</span>
            }

<span class="fc" id="L179">            return this;</span>
        }

        /**
         * Set the maximum number of connections per node.
         *
         * @param maxConnections The maximum number of connections
         * @return The builder instance
         */
        public Builder maxConnectionsPerNode(int maxConnections) {
<span class="fc" id="L189">            config.maxConnectionsPerNode = maxConnections;</span>
<span class="fc" id="L190">            return this;</span>
        }

        /**
         * Set the connection timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder connectionTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L201">            config.connectionTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L202">            return this;</span>
        }

        /**
         * Set the read timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder readTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L213">            config.readTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L214">            return this;</span>
        }

        /**
         * Set the write timeout.
         *
         * @param timeout The timeout value
         * @param unit    The time unit
         * @return The builder instance
         */
        public Builder writeTimeout(long timeout, TimeUnit unit) {
<span class="fc" id="L225">            config.writeTimeoutMs = unit.toMillis(timeout);</span>
<span class="fc" id="L226">            return this;</span>
        }

        /**
         * Set the maximum number of retries.
         *
         * @param maxRetries The maximum number of retries
         * @return The builder instance
         */
        public Builder maxRetries(int maxRetries) {
<span class="fc" id="L236">            config.maxRetries = maxRetries;</span>
<span class="fc" id="L237">            return this;</span>
        }

        /**
         * Set the retry delay.
         *
         * @param delay The delay value
         * @param unit  The time unit
         * @return The builder instance
         */
        public Builder retryDelay(long delay, TimeUnit unit) {
<span class="fc" id="L248">            config.retryDelayMs = unit.toMillis(delay);</span>
<span class="fc" id="L249">            return this;</span>
        }

        /**
         * Enable secure connection using TLS.
         *
         * @param tlsCertPath Path to the TLS certificate
         * @return The builder instance
         */
        public Builder useSecureConnection(String tlsCertPath) {
<span class="fc" id="L259">            config.useSecureConnection = true;</span>
<span class="fc" id="L260">            config.tlsCertPath = tlsCertPath;</span>
<span class="fc" id="L261">            return this;</span>
        }

        /**
         * Set authentication credentials.
         *
         * @param username The username for authentication
         * @param password The password for authentication
         * @return The builder instance
         */
        public Builder authentication(String username, String password) {
<span class="fc" id="L272">            config.username = username;</span>
<span class="fc" id="L273">            config.password = password;</span>
<span class="fc" id="L274">            return this;</span>
        }

        /**
         * Apply high-throughput optimized settings.
         * This preset is optimized for maximum performance in high-load scenarios.
         *
         * @return The builder instance
         */
        public Builder highThroughputPreset() {
<span class="fc" id="L284">            config.maxConnectionsPerNode = 50;</span>
<span class="fc" id="L285">            config.connectionTimeoutMs = 1000;</span>
<span class="fc" id="L286">            config.readTimeoutMs = 1000;</span>
<span class="fc" id="L287">            config.writeTimeoutMs = 1000;</span>
<span class="fc" id="L288">            config.maxRetries = 1;</span>
<span class="fc" id="L289">            config.retryDelayMs = 50;</span>
<span class="fc" id="L290">            return this;</span>
        }

        /**
         * Apply low-latency optimized settings.
         * This preset is optimized for minimal latency at the cost of some throughput.
         *
         * @return The builder instance
         */
        public Builder lowLatencyPreset() {
<span class="fc" id="L300">            config.maxConnectionsPerNode = 10;</span>
<span class="fc" id="L301">            config.connectionTimeoutMs = 500;</span>
<span class="fc" id="L302">            config.readTimeoutMs = 500;</span>
<span class="fc" id="L303">            config.writeTimeoutMs = 500;</span>
<span class="fc" id="L304">            config.maxRetries = 0;</span>
<span class="fc" id="L305">            config.retryDelayMs = 0;</span>
<span class="fc" id="L306">            return this;</span>
        }

        /**
         * Apply balanced settings for general use.
         * This preset balances throughput, latency, and reliability.
         *
         * @return The builder instance
         */
        public Builder balancedPreset() {
<span class="fc" id="L316">            config.maxConnectionsPerNode = 20;</span>
<span class="fc" id="L317">            config.connectionTimeoutMs = 3000;</span>
<span class="fc" id="L318">            config.readTimeoutMs = 2000;</span>
<span class="fc" id="L319">            config.writeTimeoutMs = 2000;</span>
<span class="fc" id="L320">            config.maxRetries = 2;</span>
<span class="fc" id="L321">            config.retryDelayMs = 100;</span>
<span class="fc" id="L322">            return this;</span>
        }

        /**
         * Build the configuration.
         *
         * @return The built configuration
         */
        public RustyClusterClientConfig build() {
<span class="fc bfc" id="L331" title="All 2 branches covered.">            if (config.nodes.isEmpty()) {</span>
<span class="fc" id="L332">                throw new IllegalStateException(&quot;At least one node must be configured&quot;);</span>
            }
<span class="fc" id="L334">            return config;</span>
        }
    }

    /**
     * Create a new builder for RustyClusterClientConfig.
     *
     * @return A new builder instance
     */
    public static Builder builder() {
<span class="fc" id="L344">        return new Builder();</span>
    }

    // Getters
    public List&lt;NodeConfig&gt; getNodes() {
<span class="fc" id="L349">        return Collections.unmodifiableList(nodes);</span>
    }

    public int getMaxConnectionsPerNode() {
<span class="fc" id="L353">        return maxConnectionsPerNode;</span>
    }

    public long getConnectionTimeoutMs() {
<span class="fc" id="L357">        return connectionTimeoutMs;</span>
    }

    public long getReadTimeoutMs() {
<span class="fc" id="L361">        return readTimeoutMs;</span>
    }

    public long getWriteTimeoutMs() {
<span class="fc" id="L365">        return writeTimeoutMs;</span>
    }

    public int getMaxRetries() {
<span class="fc" id="L369">        return maxRetries;</span>
    }

    public long getRetryDelayMs() {
<span class="fc" id="L373">        return retryDelayMs;</span>
    }

    public boolean isUseSecureConnection() {
<span class="fc" id="L377">        return useSecureConnection;</span>
    }

    public String getTlsCertPath() {
<span class="fc" id="L381">        return tlsCertPath;</span>
    }

    public String getUsername() {
<span class="fc" id="L385">        return username;</span>
    }

    public String getPassword() {
<span class="fc" id="L389">        return password;</span>
    }

    public boolean hasAuthentication() {
<span class="pc bpc" id="L393" title="1 of 4 branches missed.">        return username != null &amp;&amp; password != null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>