<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConnectionPool.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">ConnectionPool.java</span></div><h1>ConnectionPool.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.interceptor.AuthenticationInterceptor;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Connection pool for RustyCluster gRPC clients.
 */
public class ConnectionPool implements AutoCloseable {
<span class="fc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(ConnectionPool.class);</span>

    private final RustyClusterClientConfig config;
    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map&lt;String, GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt;&gt; stubPools;

    /**
     * Create a new ConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
<span class="nc" id="L39">    public ConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {</span>
<span class="nc" id="L40">        this.config = config;</span>
<span class="nc" id="L41">        this.channelFactory = new GrpcChannelFactory(config);</span>
<span class="nc" id="L42">        this.authenticationManager = authenticationManager;</span>
<span class="nc" id="L43">        this.stubPools = new HashMap&lt;&gt;();</span>

        // Initialize connection pools for each node
<span class="nc bnc" id="L46" title="All 2 branches missed.">        for (NodeConfig nodeConfig : config.getNodes()) {</span>
<span class="nc" id="L47">            GenericObjectPoolConfig&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; poolConfig = new GenericObjectPoolConfig&lt;&gt;();</span>

            // High-throughput optimizations
<span class="nc" id="L50">            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L51">            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L52">            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm</span>

            // Performance-oriented validation
<span class="nc" id="L55">            poolConfig.setTestOnBorrow(false); // Disable for performance - validate on return instead</span>
<span class="nc" id="L56">            poolConfig.setTestOnReturn(false); // Disable expensive validation</span>
<span class="nc" id="L57">            poolConfig.setTestWhileIdle(true); // Only validate idle connections</span>
<span class="nc" id="L58">            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30)); // More frequent cleanup</span>

            // High-throughput settings
<span class="nc" id="L61">            poolConfig.setBlockWhenExhausted(false); // Fail fast instead of blocking</span>
<span class="nc" id="L62">            poolConfig.setMaxWait(java.time.Duration.ofMillis(100)); // Quick timeout</span>
<span class="nc" id="L63">            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer</span>
<span class="nc" id="L64">            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead</span>

            // JMX monitoring for production
<span class="nc" id="L67">            poolConfig.setJmxEnabled(true);</span>
<span class="nc" id="L68">            poolConfig.setJmxNamePrefix(&quot;RustyCluster-&quot; + nodeConfig.getAddress());</span>

<span class="nc" id="L70">            GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; pool =</span>
                new GenericObjectPool&lt;&gt;(new StubFactory(nodeConfig), poolConfig);

<span class="nc" id="L73">            stubPools.put(nodeConfig.getAddress(), pool);</span>
<span class="nc" id="L74">            logger.info(&quot;Created connection pool for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L75">        }</span>
<span class="nc" id="L76">    }</span>

    /**
     * Borrow a client stub from the pool for the specified node.
     *
     * @param nodeConfig The node configuration
     * @return A client stub
     * @throws Exception If borrowing fails
     */
    public KeyValueServiceGrpc.KeyValueServiceBlockingStub borrowStub(NodeConfig nodeConfig) throws Exception {
<span class="nc" id="L86">        GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; pool = stubPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L88">            throw new IllegalArgumentException(&quot;No pool found for node: &quot; + nodeConfig);</span>
        }
<span class="nc" id="L90">        return pool.borrowObject();</span>
    }

    /**
     * Return a client stub to the pool.
     *
     * @param nodeConfig The node configuration
     * @param stub       The client stub to return
     */
    public void returnStub(NodeConfig nodeConfig, KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
<span class="nc" id="L100">        GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; pool = stubPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L102">            logger.warn(&quot;No pool found for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L103">            return;</span>
        }
<span class="nc" id="L105">        pool.returnObject(stub);</span>
<span class="nc" id="L106">    }</span>

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
<span class="nc bnc" id="L113" title="All 2 branches missed.">        for (GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; pool : stubPools.values()) {</span>
<span class="nc" id="L114">            pool.close();</span>
<span class="nc" id="L115">        }</span>
<span class="nc" id="L116">        stubPools.clear();</span>
<span class="nc" id="L117">        logger.info(&quot;Connection pool closed&quot;);</span>
<span class="nc" id="L118">    }</span>

    /**
     * Factory for creating and validating client stubs.
     */
    private class StubFactory extends BasePooledObjectFactory&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; {
        private final NodeConfig nodeConfig;

<span class="nc" id="L126">        StubFactory(NodeConfig nodeConfig) {</span>
<span class="nc" id="L127">            this.nodeConfig = nodeConfig;</span>
<span class="nc" id="L128">        }</span>

        @Override
        public KeyValueServiceGrpc.KeyValueServiceBlockingStub create() {
<span class="nc" id="L132">            ManagedChannel channel = channelFactory.createChannel(nodeConfig);</span>

            // Create channel with authentication interceptor
<span class="nc" id="L135">            var interceptedChannel = ClientInterceptors.intercept(channel,</span>
                    new AuthenticationInterceptor(authenticationManager));

            // Don't set deadline here - it will be set per-operation to avoid
            // &quot;ClientCall started after CallOptions deadline was exceeded&quot; errors
<span class="nc" id="L140">            return KeyValueServiceGrpc.newBlockingStub(interceptedChannel);</span>
        }

        @Override
        public PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; wrap(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) {
<span class="nc" id="L145">            return new DefaultPooledObject&lt;&gt;(stub);</span>
        }

        @Override
        public boolean validateObject(PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; p) {
            // In a real implementation, you might want to perform a health check
            // For now, we'll just return true
<span class="nc" id="L152">            return true;</span>
        }

        @Override
        public void destroyObject(PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceBlockingStub&gt; p) {
<span class="nc" id="L157">            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = p.getObject();</span>
<span class="nc" id="L158">            ManagedChannel channel = (ManagedChannel) stub.getChannel();</span>
            try {
<span class="nc" id="L160">                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);</span>
<span class="nc" id="L161">            } catch (InterruptedException e) {</span>
<span class="nc" id="L162">                logger.warn(&quot;Interrupted while shutting down channel&quot;, e);</span>
<span class="nc" id="L163">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L164">            }</span>
<span class="nc" id="L165">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>