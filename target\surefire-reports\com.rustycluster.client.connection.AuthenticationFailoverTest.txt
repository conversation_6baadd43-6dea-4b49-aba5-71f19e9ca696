-------------------------------------------------------------------------------
Test set: com.rustycluster.client.connection.AuthenticationFailoverTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 1, Errors: 2, Skipped: 0, Time elapsed: 0.510 s <<< FAILURE! -- in com.rustycluster.client.connection.AuthenticationFailoverTest
com.rustycluster.client.connection.AuthenticationFailoverTest.shouldClearAuthenticationStateOnlyWhenAuthenticationConfigured -- Time elapsed: 0.337 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.rustycluster.client.connection.AuthenticationFailoverTest.setUp(AuthenticationFailoverTest.java:51)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:186)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.connection.AuthenticationFailoverTest.shouldNotClearAuthenticationStateWhenNoFailoverOccurs -- Time elapsed: 0.015 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.rustycluster.client.connection.AuthenticationFailoverTest.setUp(AuthenticationFailoverTest.java:51)
  2. -> at com.rustycluster.client.connection.AuthenticationFailoverTest.shouldNotClearAuthenticationStateWhenNoFailoverOccurs(AuthenticationFailoverTest.java:96)
  3. -> at com.rustycluster.client.connection.AuthenticationFailoverTest.shouldNotClearAuthenticationStateWhenNoFailoverOccurs(AuthenticationFailoverTest.java:97)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:186)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

com.rustycluster.client.connection.AuthenticationFailoverTest.shouldClearAuthenticationStateWhenSwitchingNodes -- Time elapsed: 0.146 s <<< FAILURE!
org.mockito.exceptions.verification.TooManyActualInvocations: 

connectionPool.borrowStub(
    NodeConfig[host=secondary, port=50052, role=SECONDARY]
);
Wanted 1 time:
-> at com.rustycluster.client.connection.ConnectionPool.borrowStub(ConnectionPool.java:86)
But was 2 times:
-> at com.rustycluster.client.connection.ConnectionManager.isNodeAvailable(ConnectionManager.java:189)
-> at com.rustycluster.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:100)


	at com.rustycluster.client.connection.ConnectionPool.borrowStub(ConnectionPool.java:86)
	at com.rustycluster.client.connection.AuthenticationFailoverTest.shouldClearAuthenticationStateWhenSwitchingNodes(AuthenticationFailoverTest.java:89)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)

