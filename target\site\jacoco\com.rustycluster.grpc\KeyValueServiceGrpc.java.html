<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KeyValueServiceGrpc.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.grpc</a> &gt; <span class="el_source">KeyValueServiceGrpc.java</span></div><h1>KeyValueServiceGrpc.java</h1><pre class="source lang-java linenums">package com.rustycluster.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = &quot;by gRPC proto compiler (version 1.59.0)&quot;,
    comments = &quot;Source: rustycluster.proto&quot;)
@io.grpc.stub.annotations.GrpcGenerated
public final class KeyValueServiceGrpc {

  private KeyValueServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = &quot;rustycluster.KeyValueService&quot;;

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest,
      com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; getAuthenticateMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;Authenticate&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest,
      com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; getAuthenticateMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest, com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; getAuthenticateMethod;
    if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getAuthenticateMethod = KeyValueServiceGrpc.getAuthenticateMethod) == null) {
          KeyValueServiceGrpc.getAuthenticateMethod = getAuthenticateMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest, com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;Authenticate&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;Authenticate&quot;))
              .build();
        }
      }
    }
    return getAuthenticateMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.PingRequest,
      com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; getPingMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;Ping&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.PingRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.PingResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.PingRequest,
      com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; getPingMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.PingRequest, com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; getPingMethod;
    if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getPingMethod = KeyValueServiceGrpc.getPingMethod) == null) {
          KeyValueServiceGrpc.getPingMethod = getPingMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.PingRequest, com.rustycluster.grpc.RustyClusterProto.PingResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;Ping&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.PingRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.PingResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;Ping&quot;))
              .build();
        }
      }
    }
    return getPingMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetRequest,
      com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; getSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;Set&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.SetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetRequest,
      com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; getSetMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetRequest, com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; getSetMethod;
    if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetMethod = KeyValueServiceGrpc.getSetMethod) == null) {
          KeyValueServiceGrpc.getSetMethod = getSetMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.SetRequest, com.rustycluster.grpc.RustyClusterProto.SetResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;Set&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;Set&quot;))
              .build();
        }
      }
    }
    return getSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.GetRequest,
      com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; getGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;Get&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.GetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.GetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.GetRequest,
      com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; getGetMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.GetRequest, com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; getGetMethod;
    if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getGetMethod = KeyValueServiceGrpc.getGetMethod) == null) {
          KeyValueServiceGrpc.getGetMethod = getGetMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.GetRequest, com.rustycluster.grpc.RustyClusterProto.GetResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;Get&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.GetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.GetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;Get&quot;))
              .build();
        }
      }
    }
    return getGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
      com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; getDeleteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;Delete&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.DeleteRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.DeleteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
      com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; getDeleteMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DeleteRequest, com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; getDeleteMethod;
    if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDeleteMethod = KeyValueServiceGrpc.getDeleteMethod) == null) {
          KeyValueServiceGrpc.getDeleteMethod = getDeleteMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.DeleteRequest, com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;Delete&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DeleteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DeleteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;Delete&quot;))
              .build();
        }
      }
    }
    return getDeleteMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; getSetExMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;SetEx&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.SetExRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetExResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; getSetExMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExRequest, com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; getSetExMethod;
    if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExMethod = KeyValueServiceGrpc.getSetExMethod) == null) {
          KeyValueServiceGrpc.getSetExMethod = getSetExMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.SetExRequest, com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;SetEx&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;SetEx&quot;))
              .build();
        }
      }
    }
    return getSetExMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; getSetExpiryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;SetExpiry&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
      com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; getSetExpiryMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest, com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; getSetExpiryMethod;
    if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getSetExpiryMethod = KeyValueServiceGrpc.getSetExpiryMethod) == null) {
          KeyValueServiceGrpc.getSetExpiryMethod = getSetExpiryMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest, com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;SetExpiry&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;SetExpiry&quot;))
              .build();
        }
      }
    }
    return getSetExpiryMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; getIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;IncrBy&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.IncrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.IncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; getIncrByMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByRequest, com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; getIncrByMethod;
    if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByMethod = KeyValueServiceGrpc.getIncrByMethod) == null) {
          KeyValueServiceGrpc.getIncrByMethod = getIncrByMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.IncrByRequest, com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;IncrBy&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;IncrBy&quot;))
              .build();
        }
      }
    }
    return getIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; getDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;DecrBy&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.DecrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.DecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; getDecrByMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.DecrByRequest, com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; getDecrByMethod;
    if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getDecrByMethod = KeyValueServiceGrpc.getDecrByMethod) == null) {
          KeyValueServiceGrpc.getDecrByMethod = getDecrByMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.DecrByRequest, com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;DecrBy&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.DecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;DecrBy&quot;))
              .build();
        }
      }
    }
    return getDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; getIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;IncrByFloat&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; getIncrByFloatMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; getIncrByFloatMethod;
    if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getIncrByFloatMethod = KeyValueServiceGrpc.getIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getIncrByFloatMethod = getIncrByFloatMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;IncrByFloat&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;IncrByFloat&quot;))
              .build();
        }
      }
    }
    return getIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HSetRequest,
      com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; getHSetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HSet&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HSetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HSetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HSetRequest,
      com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; getHSetMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HSetRequest, com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; getHSetMethod;
    if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHSetMethod = KeyValueServiceGrpc.getHSetMethod) == null) {
          KeyValueServiceGrpc.getHSetMethod = getHSetMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HSetRequest, com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HSet&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HSetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HSetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HSet&quot;))
              .build();
        }
      }
    }
    return getHSetMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; getHGetMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HGet&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HGetRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HGetResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; getHGetMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetRequest, com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; getHGetMethod;
    if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetMethod = KeyValueServiceGrpc.getHGetMethod) == null) {
          KeyValueServiceGrpc.getHGetMethod = getHGetMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HGetRequest, com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HGet&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HGet&quot;))
              .build();
        }
      }
    }
    return getHGetMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; getHGetAllMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HGetAll&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HGetAllRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HGetAllResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
      com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; getHGetAllMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllRequest, com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; getHGetAllMethod;
    if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHGetAllMethod = KeyValueServiceGrpc.getHGetAllMethod) == null) {
          KeyValueServiceGrpc.getHGetAllMethod = getHGetAllMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllRequest, com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HGetAll&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetAllRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HGetAllResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HGetAll&quot;))
              .build();
        }
      }
    }
    return getHGetAllMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; getHIncrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HIncrBy&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HIncrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HIncrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; getHIncrByMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; getHIncrByMethod;
    if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByMethod = KeyValueServiceGrpc.getHIncrByMethod) == null) {
          KeyValueServiceGrpc.getHIncrByMethod = getHIncrByMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HIncrBy&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HIncrBy&quot;))
              .build();
        }
      }
    }
    return getHIncrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; getHDecrByMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HDecrBy&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HDecrByRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HDecrByResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
      com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; getHDecrByMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByRequest, com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; getHDecrByMethod;
    if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHDecrByMethod = KeyValueServiceGrpc.getHDecrByMethod) == null) {
          KeyValueServiceGrpc.getHDecrByMethod = getHDecrByMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByRequest, com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HDecrBy&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HDecrByRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HDecrByResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HDecrBy&quot;))
              .build();
        }
      }
    }
    return getHDecrByMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; getHIncrByFloatMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;HIncrByFloat&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
      com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; getHIncrByFloatMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; getHIncrByFloatMethod;
    if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getHIncrByFloatMethod = KeyValueServiceGrpc.getHIncrByFloatMethod) == null) {
          KeyValueServiceGrpc.getHIncrByFloatMethod = getHIncrByFloatMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest, com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;HIncrByFloat&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;HIncrByFloat&quot;))
              .build();
        }
      }
    }
    return getHIncrByFloatMethod;
  }

  private static volatile io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
      com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; getBatchWriteMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;BatchWrite&quot;,
      requestType = com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest.class,
      responseType = com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
      com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; getBatchWriteMethod() {
    io.grpc.MethodDescriptor&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest, com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; getBatchWriteMethod;
    if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
      synchronized (KeyValueServiceGrpc.class) {
        if ((getBatchWriteMethod = KeyValueServiceGrpc.getBatchWriteMethod) == null) {
          KeyValueServiceGrpc.getBatchWriteMethod = getBatchWriteMethod =
              io.grpc.MethodDescriptor.&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest, com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;BatchWrite&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse.getDefaultInstance()))
              .setSchemaDescriptor(new KeyValueServiceMethodDescriptorSupplier(&quot;BatchWrite&quot;))
              .build();
        }
      }
    }
    return getBatchWriteMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static KeyValueServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceStub&gt; factory =
<span class="nc" id="L550">      new io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceStub&gt;() {</span>
        @java.lang.Override
        public KeyValueServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L553">          return new KeyValueServiceStub(channel, callOptions);</span>
        }
      };
    return KeyValueServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static KeyValueServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceBlockingStub&gt; factory =
<span class="nc" id="L565">      new io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceBlockingStub&gt;() {</span>
        @java.lang.Override
        public KeyValueServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L568">          return new KeyValueServiceBlockingStub(channel, callOptions);</span>
        }
      };
    return KeyValueServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static KeyValueServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceFutureStub&gt; factory =
<span class="nc" id="L580">      new io.grpc.stub.AbstractStub.StubFactory&lt;KeyValueServiceFutureStub&gt;() {</span>
        @java.lang.Override
        public KeyValueServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L583">          return new KeyValueServiceFutureStub(channel, callOptions);</span>
        }
      };
    return KeyValueServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     * &lt;pre&gt;
     * Authentication operations
     * &lt;/pre&gt;
     */
    default void authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; responseObserver) {
<span class="nc" id="L600">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAuthenticateMethod(), responseObserver);</span>
<span class="nc" id="L601">    }</span>

    /**
     * &lt;pre&gt;
     * System operations
     * &lt;/pre&gt;
     */
    default void ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; responseObserver) {
<span class="nc" id="L610">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getPingMethod(), responseObserver);</span>
<span class="nc" id="L611">    }</span>

    /**
     * &lt;pre&gt;
     * String operations
     * &lt;/pre&gt;
     */
    default void set(com.rustycluster.grpc.RustyClusterProto.SetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; responseObserver) {
<span class="nc" id="L620">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetMethod(), responseObserver);</span>
<span class="nc" id="L621">    }</span>

    /**
     */
    default void get(com.rustycluster.grpc.RustyClusterProto.GetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; responseObserver) {
<span class="nc" id="L627">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetMethod(), responseObserver);</span>
<span class="nc" id="L628">    }</span>

    /**
     */
    default void delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; responseObserver) {
<span class="nc" id="L634">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteMethod(), responseObserver);</span>
<span class="nc" id="L635">    }</span>

    /**
     */
    default void setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; responseObserver) {
<span class="nc" id="L641">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExMethod(), responseObserver);</span>
<span class="nc" id="L642">    }</span>

    /**
     */
    default void setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; responseObserver) {
<span class="nc" id="L648">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSetExpiryMethod(), responseObserver);</span>
<span class="nc" id="L649">    }</span>

    /**
     * &lt;pre&gt;
     * Numeric operations
     * &lt;/pre&gt;
     */
    default void incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; responseObserver) {
<span class="nc" id="L658">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByMethod(), responseObserver);</span>
<span class="nc" id="L659">    }</span>

    /**
     */
    default void decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; responseObserver) {
<span class="nc" id="L665">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDecrByMethod(), responseObserver);</span>
<span class="nc" id="L666">    }</span>

    /**
     */
    default void incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; responseObserver) {
<span class="nc" id="L672">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getIncrByFloatMethod(), responseObserver);</span>
<span class="nc" id="L673">    }</span>

    /**
     * &lt;pre&gt;
     * Hash operations
     * &lt;/pre&gt;
     */
    default void hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; responseObserver) {
<span class="nc" id="L682">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHSetMethod(), responseObserver);</span>
<span class="nc" id="L683">    }</span>

    /**
     */
    default void hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; responseObserver) {
<span class="nc" id="L689">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetMethod(), responseObserver);</span>
<span class="nc" id="L690">    }</span>

    /**
     */
    default void hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; responseObserver) {
<span class="nc" id="L696">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHGetAllMethod(), responseObserver);</span>
<span class="nc" id="L697">    }</span>

    /**
     */
    default void hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; responseObserver) {
<span class="nc" id="L703">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByMethod(), responseObserver);</span>
<span class="nc" id="L704">    }</span>

    /**
     */
    default void hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; responseObserver) {
<span class="nc" id="L710">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHDecrByMethod(), responseObserver);</span>
<span class="nc" id="L711">    }</span>

    /**
     */
    default void hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; responseObserver) {
<span class="nc" id="L717">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getHIncrByFloatMethod(), responseObserver);</span>
<span class="nc" id="L718">    }</span>

    /**
     * &lt;pre&gt;
     * Batch operations
     * &lt;/pre&gt;
     */
    default void batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; responseObserver) {
<span class="nc" id="L727">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBatchWriteMethod(), responseObserver);</span>
<span class="nc" id="L728">    }</span>
  }

  /**
   * Base class for the server implementation of the service KeyValueService.
   */
<span class="nc" id="L734">  public static abstract class KeyValueServiceImplBase</span>
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
<span class="nc" id="L738">      return KeyValueServiceGrpc.bindService(this);</span>
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceStub
      extends io.grpc.stub.AbstractAsyncStub&lt;KeyValueServiceStub&gt; {
    private KeyValueServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L749">      super(channel, callOptions);</span>
<span class="nc" id="L750">    }</span>

    @java.lang.Override
    protected KeyValueServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L755">      return new KeyValueServiceStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Authentication operations
     * &lt;/pre&gt;
     */
    public void authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; responseObserver) {
<span class="nc" id="L765">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L766">          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L767">    }</span>

    /**
     * &lt;pre&gt;
     * System operations
     * &lt;/pre&gt;
     */
    public void ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; responseObserver) {
<span class="nc" id="L776">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L777">          getChannel().newCall(getPingMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L778">    }</span>

    /**
     * &lt;pre&gt;
     * String operations
     * &lt;/pre&gt;
     */
    public void set(com.rustycluster.grpc.RustyClusterProto.SetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; responseObserver) {
<span class="nc" id="L787">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L788">          getChannel().newCall(getSetMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L789">    }</span>

    /**
     */
    public void get(com.rustycluster.grpc.RustyClusterProto.GetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; responseObserver) {
<span class="nc" id="L795">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L796">          getChannel().newCall(getGetMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L797">    }</span>

    /**
     */
    public void delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; responseObserver) {
<span class="nc" id="L803">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L804">          getChannel().newCall(getDeleteMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L805">    }</span>

    /**
     */
    public void setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; responseObserver) {
<span class="nc" id="L811">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L812">          getChannel().newCall(getSetExMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L813">    }</span>

    /**
     */
    public void setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; responseObserver) {
<span class="nc" id="L819">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L820">          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L821">    }</span>

    /**
     * &lt;pre&gt;
     * Numeric operations
     * &lt;/pre&gt;
     */
    public void incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; responseObserver) {
<span class="nc" id="L830">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L831">          getChannel().newCall(getIncrByMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L832">    }</span>

    /**
     */
    public void decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; responseObserver) {
<span class="nc" id="L838">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L839">          getChannel().newCall(getDecrByMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L840">    }</span>

    /**
     */
    public void incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; responseObserver) {
<span class="nc" id="L846">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L847">          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L848">    }</span>

    /**
     * &lt;pre&gt;
     * Hash operations
     * &lt;/pre&gt;
     */
    public void hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; responseObserver) {
<span class="nc" id="L857">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L858">          getChannel().newCall(getHSetMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L859">    }</span>

    /**
     */
    public void hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; responseObserver) {
<span class="nc" id="L865">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L866">          getChannel().newCall(getHGetMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L867">    }</span>

    /**
     */
    public void hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; responseObserver) {
<span class="nc" id="L873">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L874">          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L875">    }</span>

    /**
     */
    public void hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; responseObserver) {
<span class="nc" id="L881">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L882">          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L883">    }</span>

    /**
     */
    public void hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; responseObserver) {
<span class="nc" id="L889">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L890">          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L891">    }</span>

    /**
     */
    public void hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; responseObserver) {
<span class="nc" id="L897">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L898">          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L899">    }</span>

    /**
     * &lt;pre&gt;
     * Batch operations
     * &lt;/pre&gt;
     */
    public void batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request,
        io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; responseObserver) {
<span class="nc" id="L908">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L909">          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L910">    }</span>
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub&lt;KeyValueServiceBlockingStub&gt; {
    private KeyValueServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L920">      super(channel, callOptions);</span>
<span class="nc" id="L921">    }</span>

    @java.lang.Override
    protected KeyValueServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L926">      return new KeyValueServiceBlockingStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Authentication operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse authenticate(com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request) {
<span class="nc" id="L935">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L936">          getChannel(), getAuthenticateMethod(), getCallOptions(), request);</span>
    }

    /**
     * &lt;pre&gt;
     * System operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.PingResponse ping(com.rustycluster.grpc.RustyClusterProto.PingRequest request) {
<span class="nc" id="L945">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L946">          getChannel(), getPingMethod(), getCallOptions(), request);</span>
    }

    /**
     * &lt;pre&gt;
     * String operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.SetResponse set(com.rustycluster.grpc.RustyClusterProto.SetRequest request) {
<span class="nc" id="L955">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L956">          getChannel(), getSetMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.GetResponse get(com.rustycluster.grpc.RustyClusterProto.GetRequest request) {
<span class="nc" id="L962">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L963">          getChannel(), getGetMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.DeleteResponse delete(com.rustycluster.grpc.RustyClusterProto.DeleteRequest request) {
<span class="nc" id="L969">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L970">          getChannel(), getDeleteMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.SetExResponse setEx(com.rustycluster.grpc.RustyClusterProto.SetExRequest request) {
<span class="nc" id="L976">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L977">          getChannel(), getSetExMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse setExpiry(com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request) {
<span class="nc" id="L983">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L984">          getChannel(), getSetExpiryMethod(), getCallOptions(), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Numeric operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.IncrByResponse incrBy(com.rustycluster.grpc.RustyClusterProto.IncrByRequest request) {
<span class="nc" id="L993">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L994">          getChannel(), getIncrByMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.DecrByResponse decrBy(com.rustycluster.grpc.RustyClusterProto.DecrByRequest request) {
<span class="nc" id="L1000">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1001">          getChannel(), getDecrByMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse incrByFloat(com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request) {
<span class="nc" id="L1007">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1008">          getChannel(), getIncrByFloatMethod(), getCallOptions(), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Hash operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.HSetResponse hSet(com.rustycluster.grpc.RustyClusterProto.HSetRequest request) {
<span class="nc" id="L1017">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1018">          getChannel(), getHSetMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HGetResponse hGet(com.rustycluster.grpc.RustyClusterProto.HGetRequest request) {
<span class="nc" id="L1024">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1025">          getChannel(), getHGetMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HGetAllResponse hGetAll(com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request) {
<span class="nc" id="L1031">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1032">          getChannel(), getHGetAllMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HIncrByResponse hIncrBy(com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request) {
<span class="nc" id="L1038">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1039">          getChannel(), getHIncrByMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HDecrByResponse hDecrBy(com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request) {
<span class="nc" id="L1045">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1046">          getChannel(), getHDecrByMethod(), getCallOptions(), request);</span>
    }

    /**
     */
    public com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse hIncrByFloat(com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request) {
<span class="nc" id="L1052">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1053">          getChannel(), getHIncrByFloatMethod(), getCallOptions(), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Batch operations
     * &lt;/pre&gt;
     */
    public com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse batchWrite(com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request) {
<span class="nc" id="L1062">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L1063">          getChannel(), getBatchWriteMethod(), getCallOptions(), request);</span>
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service KeyValueService.
   */
  public static final class KeyValueServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub&lt;KeyValueServiceFutureStub&gt; {
    private KeyValueServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L1074">      super(channel, callOptions);</span>
<span class="nc" id="L1075">    }</span>

    @java.lang.Override
    protected KeyValueServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L1080">      return new KeyValueServiceFutureStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Authentication operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt; authenticate(
        com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest request) {
<span class="nc" id="L1090">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1091">          getChannel().newCall(getAuthenticateMethod(), getCallOptions()), request);</span>
    }

    /**
     * &lt;pre&gt;
     * System operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.PingResponse&gt; ping(
        com.rustycluster.grpc.RustyClusterProto.PingRequest request) {
<span class="nc" id="L1101">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1102">          getChannel().newCall(getPingMethod(), getCallOptions()), request);</span>
    }

    /**
     * &lt;pre&gt;
     * String operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.SetResponse&gt; set(
        com.rustycluster.grpc.RustyClusterProto.SetRequest request) {
<span class="nc" id="L1112">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1113">          getChannel().newCall(getSetMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.GetResponse&gt; get(
        com.rustycluster.grpc.RustyClusterProto.GetRequest request) {
<span class="nc" id="L1120">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1121">          getChannel().newCall(getGetMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt; delete(
        com.rustycluster.grpc.RustyClusterProto.DeleteRequest request) {
<span class="nc" id="L1128">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1129">          getChannel().newCall(getDeleteMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt; setEx(
        com.rustycluster.grpc.RustyClusterProto.SetExRequest request) {
<span class="nc" id="L1136">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1137">          getChannel().newCall(getSetExMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt; setExpiry(
        com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest request) {
<span class="nc" id="L1144">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1145">          getChannel().newCall(getSetExpiryMethod(), getCallOptions()), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Numeric operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt; incrBy(
        com.rustycluster.grpc.RustyClusterProto.IncrByRequest request) {
<span class="nc" id="L1155">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1156">          getChannel().newCall(getIncrByMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt; decrBy(
        com.rustycluster.grpc.RustyClusterProto.DecrByRequest request) {
<span class="nc" id="L1163">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1164">          getChannel().newCall(getDecrByMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt; incrByFloat(
        com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest request) {
<span class="nc" id="L1171">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1172">          getChannel().newCall(getIncrByFloatMethod(), getCallOptions()), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Hash operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt; hSet(
        com.rustycluster.grpc.RustyClusterProto.HSetRequest request) {
<span class="nc" id="L1182">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1183">          getChannel().newCall(getHSetMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt; hGet(
        com.rustycluster.grpc.RustyClusterProto.HGetRequest request) {
<span class="nc" id="L1190">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1191">          getChannel().newCall(getHGetMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt; hGetAll(
        com.rustycluster.grpc.RustyClusterProto.HGetAllRequest request) {
<span class="nc" id="L1198">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1199">          getChannel().newCall(getHGetAllMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt; hIncrBy(
        com.rustycluster.grpc.RustyClusterProto.HIncrByRequest request) {
<span class="nc" id="L1206">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1207">          getChannel().newCall(getHIncrByMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt; hDecrBy(
        com.rustycluster.grpc.RustyClusterProto.HDecrByRequest request) {
<span class="nc" id="L1214">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1215">          getChannel().newCall(getHDecrByMethod(), getCallOptions()), request);</span>
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt; hIncrByFloat(
        com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest request) {
<span class="nc" id="L1222">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1223">          getChannel().newCall(getHIncrByFloatMethod(), getCallOptions()), request);</span>
    }

    /**
     * &lt;pre&gt;
     * Batch operations
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt; batchWrite(
        com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest request) {
<span class="nc" id="L1233">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L1234">          getChannel().newCall(getBatchWriteMethod(), getCallOptions()), request);</span>
    }
  }

  private static final int METHODID_AUTHENTICATE = 0;
  private static final int METHODID_PING = 1;
  private static final int METHODID_SET = 2;
  private static final int METHODID_GET = 3;
  private static final int METHODID_DELETE = 4;
  private static final int METHODID_SET_EX = 5;
  private static final int METHODID_SET_EXPIRY = 6;
  private static final int METHODID_INCR_BY = 7;
  private static final int METHODID_DECR_BY = 8;
  private static final int METHODID_INCR_BY_FLOAT = 9;
  private static final int METHODID_HSET = 10;
  private static final int METHODID_HGET = 11;
  private static final int METHODID_HGET_ALL = 12;
  private static final int METHODID_HINCR_BY = 13;
  private static final int METHODID_HDECR_BY = 14;
  private static final int METHODID_HINCR_BY_FLOAT = 15;
  private static final int METHODID_BATCH_WRITE = 16;

  private static final class MethodHandlers&lt;Req, Resp&gt; implements
      io.grpc.stub.ServerCalls.UnaryMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.ServerStreamingMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.ClientStreamingMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.BidiStreamingMethod&lt;Req, Resp&gt; {
    private final AsyncService serviceImpl;
    private final int methodId;

<span class="nc" id="L1264">    MethodHandlers(AsyncService serviceImpl, int methodId) {</span>
<span class="nc" id="L1265">      this.serviceImpl = serviceImpl;</span>
<span class="nc" id="L1266">      this.methodId = methodId;</span>
<span class="nc" id="L1267">    }</span>

    @java.lang.Override
    @java.lang.SuppressWarnings(&quot;unchecked&quot;)
    public void invoke(Req request, io.grpc.stub.StreamObserver&lt;Resp&gt; responseObserver) {
<span class="nc bnc" id="L1272" title="All 18 branches missed.">      switch (methodId) {</span>
        case METHODID_AUTHENTICATE:
<span class="nc" id="L1274">          serviceImpl.authenticate((com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest) request,</span>
<span class="nc" id="L1275">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt;) responseObserver);</span>
<span class="nc" id="L1276">          break;</span>
        case METHODID_PING:
<span class="nc" id="L1278">          serviceImpl.ping((com.rustycluster.grpc.RustyClusterProto.PingRequest) request,</span>
<span class="nc" id="L1279">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.PingResponse&gt;) responseObserver);</span>
<span class="nc" id="L1280">          break;</span>
        case METHODID_SET:
<span class="nc" id="L1282">          serviceImpl.set((com.rustycluster.grpc.RustyClusterProto.SetRequest) request,</span>
<span class="nc" id="L1283">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetResponse&gt;) responseObserver);</span>
<span class="nc" id="L1284">          break;</span>
        case METHODID_GET:
<span class="nc" id="L1286">          serviceImpl.get((com.rustycluster.grpc.RustyClusterProto.GetRequest) request,</span>
<span class="nc" id="L1287">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.GetResponse&gt;) responseObserver);</span>
<span class="nc" id="L1288">          break;</span>
        case METHODID_DELETE:
<span class="nc" id="L1290">          serviceImpl.delete((com.rustycluster.grpc.RustyClusterProto.DeleteRequest) request,</span>
<span class="nc" id="L1291">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt;) responseObserver);</span>
<span class="nc" id="L1292">          break;</span>
        case METHODID_SET_EX:
<span class="nc" id="L1294">          serviceImpl.setEx((com.rustycluster.grpc.RustyClusterProto.SetExRequest) request,</span>
<span class="nc" id="L1295">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt;) responseObserver);</span>
<span class="nc" id="L1296">          break;</span>
        case METHODID_SET_EXPIRY:
<span class="nc" id="L1298">          serviceImpl.setExpiry((com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest) request,</span>
<span class="nc" id="L1299">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt;) responseObserver);</span>
<span class="nc" id="L1300">          break;</span>
        case METHODID_INCR_BY:
<span class="nc" id="L1302">          serviceImpl.incrBy((com.rustycluster.grpc.RustyClusterProto.IncrByRequest) request,</span>
<span class="nc" id="L1303">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt;) responseObserver);</span>
<span class="nc" id="L1304">          break;</span>
        case METHODID_DECR_BY:
<span class="nc" id="L1306">          serviceImpl.decrBy((com.rustycluster.grpc.RustyClusterProto.DecrByRequest) request,</span>
<span class="nc" id="L1307">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt;) responseObserver);</span>
<span class="nc" id="L1308">          break;</span>
        case METHODID_INCR_BY_FLOAT:
<span class="nc" id="L1310">          serviceImpl.incrByFloat((com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest) request,</span>
<span class="nc" id="L1311">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt;) responseObserver);</span>
<span class="nc" id="L1312">          break;</span>
        case METHODID_HSET:
<span class="nc" id="L1314">          serviceImpl.hSet((com.rustycluster.grpc.RustyClusterProto.HSetRequest) request,</span>
<span class="nc" id="L1315">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt;) responseObserver);</span>
<span class="nc" id="L1316">          break;</span>
        case METHODID_HGET:
<span class="nc" id="L1318">          serviceImpl.hGet((com.rustycluster.grpc.RustyClusterProto.HGetRequest) request,</span>
<span class="nc" id="L1319">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt;) responseObserver);</span>
<span class="nc" id="L1320">          break;</span>
        case METHODID_HGET_ALL:
<span class="nc" id="L1322">          serviceImpl.hGetAll((com.rustycluster.grpc.RustyClusterProto.HGetAllRequest) request,</span>
<span class="nc" id="L1323">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt;) responseObserver);</span>
<span class="nc" id="L1324">          break;</span>
        case METHODID_HINCR_BY:
<span class="nc" id="L1326">          serviceImpl.hIncrBy((com.rustycluster.grpc.RustyClusterProto.HIncrByRequest) request,</span>
<span class="nc" id="L1327">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt;) responseObserver);</span>
<span class="nc" id="L1328">          break;</span>
        case METHODID_HDECR_BY:
<span class="nc" id="L1330">          serviceImpl.hDecrBy((com.rustycluster.grpc.RustyClusterProto.HDecrByRequest) request,</span>
<span class="nc" id="L1331">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt;) responseObserver);</span>
<span class="nc" id="L1332">          break;</span>
        case METHODID_HINCR_BY_FLOAT:
<span class="nc" id="L1334">          serviceImpl.hIncrByFloat((com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest) request,</span>
<span class="nc" id="L1335">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt;) responseObserver);</span>
<span class="nc" id="L1336">          break;</span>
        case METHODID_BATCH_WRITE:
<span class="nc" id="L1338">          serviceImpl.batchWrite((com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest) request,</span>
<span class="nc" id="L1339">              (io.grpc.stub.StreamObserver&lt;com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt;) responseObserver);</span>
<span class="nc" id="L1340">          break;</span>
        default:
<span class="nc" id="L1342">          throw new AssertionError();</span>
      }
<span class="nc" id="L1344">    }</span>

    @java.lang.Override
    @java.lang.SuppressWarnings(&quot;unchecked&quot;)
    public io.grpc.stub.StreamObserver&lt;Req&gt; invoke(
        io.grpc.stub.StreamObserver&lt;Resp&gt; responseObserver) {
<span class="nc" id="L1350">      switch (methodId) {</span>
        default:
<span class="nc" id="L1352">          throw new AssertionError();</span>
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getAuthenticateMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.AuthenticateRequest,
              com.rustycluster.grpc.RustyClusterProto.AuthenticateResponse&gt;(
                service, METHODID_AUTHENTICATE)))
        .addMethod(
          getPingMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.PingRequest,
              com.rustycluster.grpc.RustyClusterProto.PingResponse&gt;(
                service, METHODID_PING)))
        .addMethod(
          getSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.SetRequest,
              com.rustycluster.grpc.RustyClusterProto.SetResponse&gt;(
                service, METHODID_SET)))
        .addMethod(
          getGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.GetRequest,
              com.rustycluster.grpc.RustyClusterProto.GetResponse&gt;(
                service, METHODID_GET)))
        .addMethod(
          getDeleteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.DeleteRequest,
              com.rustycluster.grpc.RustyClusterProto.DeleteResponse&gt;(
                service, METHODID_DELETE)))
        .addMethod(
          getSetExMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.SetExRequest,
              com.rustycluster.grpc.RustyClusterProto.SetExResponse&gt;(
                service, METHODID_SET_EX)))
        .addMethod(
          getSetExpiryMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.SetExpiryRequest,
              com.rustycluster.grpc.RustyClusterProto.SetExpiryResponse&gt;(
                service, METHODID_SET_EXPIRY)))
        .addMethod(
          getIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.IncrByRequest,
              com.rustycluster.grpc.RustyClusterProto.IncrByResponse&gt;(
                service, METHODID_INCR_BY)))
        .addMethod(
          getDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.DecrByRequest,
              com.rustycluster.grpc.RustyClusterProto.DecrByResponse&gt;(
                service, METHODID_DECR_BY)))
        .addMethod(
          getIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.IncrByFloatRequest,
              com.rustycluster.grpc.RustyClusterProto.IncrByFloatResponse&gt;(
                service, METHODID_INCR_BY_FLOAT)))
        .addMethod(
          getHSetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HSetRequest,
              com.rustycluster.grpc.RustyClusterProto.HSetResponse&gt;(
                service, METHODID_HSET)))
        .addMethod(
          getHGetMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HGetRequest,
              com.rustycluster.grpc.RustyClusterProto.HGetResponse&gt;(
                service, METHODID_HGET)))
        .addMethod(
          getHGetAllMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HGetAllRequest,
              com.rustycluster.grpc.RustyClusterProto.HGetAllResponse&gt;(
                service, METHODID_HGET_ALL)))
        .addMethod(
          getHIncrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HIncrByRequest,
              com.rustycluster.grpc.RustyClusterProto.HIncrByResponse&gt;(
                service, METHODID_HINCR_BY)))
        .addMethod(
          getHDecrByMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HDecrByRequest,
              com.rustycluster.grpc.RustyClusterProto.HDecrByResponse&gt;(
                service, METHODID_HDECR_BY)))
        .addMethod(
          getHIncrByFloatMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.HIncrByFloatRequest,
              com.rustycluster.grpc.RustyClusterProto.HIncrByFloatResponse&gt;(
                service, METHODID_HINCR_BY_FLOAT)))
        .addMethod(
          getBatchWriteMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.rustycluster.grpc.RustyClusterProto.BatchWriteRequest,
              com.rustycluster.grpc.RustyClusterProto.BatchWriteResponse&gt;(
                service, METHODID_BATCH_WRITE)))
        .build();
  }

  private static abstract class KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
<span class="nc" id="L1483">    KeyValueServiceBaseDescriptorSupplier() {}</span>

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
<span class="nc" id="L1487">      return com.rustycluster.grpc.RustyClusterProto.getDescriptor();</span>
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
<span class="nc" id="L1492">      return getFileDescriptor().findServiceByName(&quot;KeyValueService&quot;);</span>
    }
  }

  private static final class KeyValueServiceFileDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier {
<span class="nc" id="L1498">    KeyValueServiceFileDescriptorSupplier() {}</span>
  }

  private static final class KeyValueServiceMethodDescriptorSupplier
      extends KeyValueServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

<span class="nc" id="L1506">    KeyValueServiceMethodDescriptorSupplier(java.lang.String methodName) {</span>
<span class="nc" id="L1507">      this.methodName = methodName;</span>
<span class="nc" id="L1508">    }</span>

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
<span class="nc" id="L1512">      return getServiceDescriptor().findMethodByName(methodName);</span>
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (KeyValueServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new KeyValueServiceFileDescriptorSupplier())
              .addMethod(getAuthenticateMethod())
              .addMethod(getPingMethod())
              .addMethod(getSetMethod())
              .addMethod(getGetMethod())
              .addMethod(getDeleteMethod())
              .addMethod(getSetExMethod())
              .addMethod(getSetExpiryMethod())
              .addMethod(getIncrByMethod())
              .addMethod(getDecrByMethod())
              .addMethod(getIncrByFloatMethod())
              .addMethod(getHSetMethod())
              .addMethod(getHGetMethod())
              .addMethod(getHGetAllMethod())
              .addMethod(getHIncrByMethod())
              .addMethod(getHDecrByMethod())
              .addMethod(getHIncrByFloatMethod())
              .addMethod(getBatchWriteMethod())
              .build();
        }
      }
    }
    return result;
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>