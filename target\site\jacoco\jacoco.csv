GRO<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
RustyCluster Java Client,com.rustycluster.client,BatchOperationBuilder,0,198,0,0,0,85,0,13,0,13
RustyCluster Java Client,com.rustycluster.client,RustyClusterAsyncClient,248,0,4,0,74,0,26,0,24,0
RustyCluster Java Client,com.rustycluster.client,Rusty<PERSON>lusterClient,390,251,4,2,101,74,30,23,28,22
RustyCluster Java Client,com.rustycluster.client.config,RustyClusterClientConfig,0,86,1,3,0,25,1,15,0,14
RustyCluster Java Client,com.rustycluster.client.config,NodeConfig,0,18,0,0,0,2,0,2,0,2
RustyCluster Java Client,com.rustycluster.client.config,RustyClusterClientConfig.Builder,16,330,3,15,4,80,3,28,0,22
RustyCluster Java Client,com.rustycluster.client.config,NodeRole,0,35,0,0,0,8,0,3,0,3
RustyCluster Java Client,com.rustycluster.client.auth,AuthenticationManager,34,86,1,3,9,26,1,7,0,6
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetResponse,370,0,32,0,107,0,51,0,35,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByRequest,512,0,48,0,145,0,62,0,38,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExRequest.Builder,433,124,44,8,133,39,58,8,30,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteRequest,297,73,28,4,84,23,43,8,27,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatResponse,291,0,22,0,82,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByRequest.Builder,323,98,33,6,99,31,47,7,26,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByResponse,286,0,22,0,81,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByRequest.Builder,557,0,52,0,172,0,66,0,38,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllResponse,333,56,32,4,91,19,50,8,32,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingRequest.Builder,149,0,14,0,48,0,27,0,20,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryResponse,281,0,22,0,81,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateRequest.Builder,355,82,36,6,111,27,48,6,26,6
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteResponse.Builder,333,98,32,5,98,31,47,7,27,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExResponse,226,55,19,3,63,18,37,7,26,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByRequest.Builder,557,0,52,0,172,0,66,0,38,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryRequest.Builder,421,0,39,0,130,0,54,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByResponse.Builder,223,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatResponse.Builder,223,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateRequest,347,64,33,3,101,20,48,6,30,6
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetRequest.Builder,393,103,40,7,122,33,53,7,28,7
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceFutureStub,164,0,0,0,37,0,19,0,19,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetRequest,411,0,36,0,121,0,54,0,36,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchOperation,672,144,97,7,172,41,86,13,34,13
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchOperation.OperationType,55,180,8,12,17,43,11,14,6,4
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetResponse,226,55,19,3,63,18,37,7,26,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByRequest,341,82,34,4,94,25,46,9,27,9
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatRequest.Builder,557,0,52,0,172,0,66,0,38,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceFileDescriptorSupplier,3,0,0,0,1,0,1,0,1,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetRequest.Builder,632,0,60,0,197,0,72,0,40,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllResponse.Builder,335,85,36,9,93,27,52,7,30,6
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceBlockingStub,164,0,0,0,37,0,19,0,19,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetRequest,258,64,22,4,74,21,40,7,27,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceImplBase,6,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllRequest.Builder,245,56,25,4,77,19,37,5,22,5
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.AsyncService,68,0,0,0,34,0,17,0,17,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteRequest,305,62,27,3,77,20,45,8,30,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteResponse,226,55,19,3,63,18,37,7,26,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatRequest,428,0,38,0,120,0,55,0,36,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteResponse,317,70,29,3,85,22,45,7,29,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByResponse.Builder,223,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetRequest,368,91,37,5,105,28,49,9,28,9
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByResponse,286,0,22,0,81,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetResponse.Builder,283,77,28,6,88,25,41,7,24,6
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchOperation.OperationType.new Internal.EnumLiteMap() {...},3,3,0,0,1,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteRequest.Builder,283,77,29,5,88,25,42,6,24,6
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryResponse.Builder,221,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingResponse,423,0,38,0,119,0,55,0,36,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByResponse.Builder,223,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateRequest.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatResponse.Builder,223,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.MethodHandlers,143,0,18,0,60,0,20,0,3,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetResponse,281,0,22,0,81,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllRequest,258,64,22,4,74,21,40,7,27,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByResponse,231,55,19,3,63,18,37,7,26,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExResponse.Builder,173,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceMethodDescriptorSupplier,12,0,0,0,4,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateResponse.Builder,393,103,40,7,122,33,53,7,28,7
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetRequest.Builder,437,0,42,0,138,0,54,0,32,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchOperation.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatRequest.Builder,421,0,39,0,130,0,54,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByResponse,286,0,22,0,81,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetRequest,548,0,52,0,159,0,65,0,39,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByResponse.Builder,175,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExRequest,412,100,43,5,115,30,52,10,28,10
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetAllResponse.FieldsDefaultEntryHolder,0,8,0,0,0,7,0,1,0,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingRequest,233,0,16,0,69,0,40,0,32,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.PingResponse.Builder,421,0,39,0,130,0,54,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatResponse,291,0,22,0,82,0,44,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryRequest,423,0,38,0,119,0,55,0,36,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DeleteResponse.Builder,173,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetResponse.Builder,173,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetResponse,297,73,28,4,84,23,43,8,27,8
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceBaseDescriptorSupplier,10,0,0,0,3,0,3,0,3,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExpiryResponse.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchWriteRequest.Builder,603,115,75,7,184,38,80,8,38,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.GetRequest.Builder,245,56,25,4,77,19,37,5,22,5
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.SetExResponse.new AbstractParser() {...},33,3,0,0,10,1,1,1,1,1
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.AuthenticateResponse,377,82,38,4,108,25,50,8,29,8
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.IncrByFloatRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto,4,811,0,0,4,223,2,2,2,2
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByRequest.Builder,421,0,39,0,130,0,54,0,33,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HSetResponse.Builder,221,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.BatchOperation.Builder,682,222,69,18,200,67,86,16,44,11
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatRequest.new AbstractParser() {...},36,0,0,0,11,0,2,0,2,0
RustyCluster Java Client,com.rustycluster.grpc,KeyValueServiceGrpc.KeyValueServiceStub,181,0,0,0,54,0,19,0,19,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.DecrByRequest,423,0,38,0,119,0,55,0,36,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HDecrByRequest,512,0,48,0,145,0,62,0,38,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HGetResponse.Builder,360,0,34,0,113,0,48,0,30,0
RustyCluster Java Client,com.rustycluster.grpc,RustyClusterProto.HIncrByFloatRequest,517,0,48,0,146,0,62,0,38,0
RustyCluster Java Client,com.rustycluster.client.exception,NoAvailableNodesException,4,5,0,0,2,2,1,1,1,1
RustyCluster Java Client,com.rustycluster.client.metrics,PerformanceMetrics,422,0,8,0,87,0,25,0,21,0
RustyCluster Java Client,com.rustycluster.client.metrics,PerformanceMetrics.PerformanceStats,48,0,0,0,1,0,1,0,1,0
RustyCluster Java Client,com.rustycluster.client.interceptor,AuthenticationInterceptor,24,0,0,0,8,0,3,0,3,0
RustyCluster Java Client,com.rustycluster.client.interceptor,AuthenticationInterceptor.new ForwardingClientCall.SimpleForwardingClientCall() {...},38,0,4,0,8,0,4,0,2,0
RustyCluster Java Client,com.rustycluster.client.example,RustyLoadExample,215,0,11,0,52,0,10,0,3,0
RustyCluster Java Client,com.rustycluster.client.example,RustyClusterClientExample,270,0,2,0,67,0,3,0,2,0
RustyCluster Java Client,com.rustycluster.client.example,HighThroughputExample,524,0,28,0,120,0,23,0,9,0
RustyCluster Java Client,com.rustycluster.client.connection,ConnectionManager,24,231,2,20,5,60,3,19,1,10
RustyCluster Java Client,com.rustycluster.client.connection,ConnectionPool.StubFactory,70,0,0,0,17,0,5,0,5,0
RustyCluster Java Client,com.rustycluster.client.connection,AsyncConnectionManager,351,0,20,0,83,0,33,0,23,0
RustyCluster Java Client,com.rustycluster.client.connection,GrpcChannelFactory,91,0,2,0,33,0,5,0,4,0
RustyCluster Java Client,com.rustycluster.client.connection,AsyncConnectionPool.AsyncStubFactory,70,0,0,0,17,0,5,0,5,0
RustyCluster Java Client,com.rustycluster.client.connection,ConnectionPool,168,4,8,0,40,1,8,1,4,1
RustyCluster Java Client,com.rustycluster.client.connection,AsyncConnectionPool,184,0,8,0,45,0,10,0,6,0
