<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HighThroughputExample.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.example</a> &gt; <span class="el_source">HighThroughputExample.java</span></div><h1>HighThroughputExample.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.example;

import com.rustycluster.client.BatchOperationBuilder;
import com.rustycluster.client.RustyClusterAsyncClient;
import com.rustycluster.client.RustyClusterClient;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Example demonstrating high-throughput usage of RustyCluster Java client.
 * This example shows various optimization techniques for maximum performance.
 */
<span class="nc" id="L22">public class HighThroughputExample {</span>
<span class="nc" id="L23">    private static final Logger logger = LoggerFactory.getLogger(HighThroughputExample.class);</span>

    public static void main(String[] args) {
        // High-throughput configuration
<span class="nc" id="L27">         RustyClusterClientConfig config = RustyClusterClientConfig.builder()</span>
<span class="nc" id="L28">                .addNodes(&quot;localhost:50051&quot;, &quot;localhost:50052&quot;, &quot;localhost:50053&quot;)</span>
<span class="nc" id="L29">                .maxConnectionsPerNode(1000)</span>
<span class="nc" id="L30">                .connectionTimeout(2, TimeUnit.SECONDS)</span>
<span class="nc" id="L31">                .readTimeout(2, TimeUnit.SECONDS)</span>
<span class="nc" id="L32">                .writeTimeout(3, TimeUnit.SECONDS)</span>
<span class="nc" id="L33">                .maxRetries(3)</span>
<span class="nc" id="L34">                .retryDelay(500, TimeUnit.MILLISECONDS)</span>
<span class="nc" id="L35">                .authentication(&quot;admin&quot;, &quot;npci&quot;)  // Add authentication credentials</span>
<span class="nc" id="L36">                .build();</span>

<span class="nc" id="L38">        logger.info(&quot;Starting high-throughput performance demonstration&quot;);</span>

        // Demonstrate different performance optimization techniques
        //demonstrateSyncBatchOperations(config);
        //demonstrateAsyncOperations(config);
<span class="nc" id="L43">        demonstrateConcurrentOperations(config);</span>

<span class="nc" id="L45">        logger.info(&quot;High-throughput demonstration completed&quot;);</span>
<span class="nc" id="L46">    }</span>

    /**
     * Demonstrate high-throughput synchronous batch operations.
     */
    private static void demonstrateSyncBatchOperations(RustyClusterClientConfig config) {
<span class="nc" id="L52">        logger.info(&quot;=== Synchronous Batch Operations Demo ===&quot;);</span>

<span class="nc" id="L54">        try (RustyClusterClient client = new RustyClusterClient(config)) {</span>
            // Create large batch operations
<span class="nc" id="L56">            int batchSize = 1000;</span>
<span class="nc" id="L57">            int numBatches = 10;</span>
<span class="nc" id="L58">            long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L59">            int totalOperations = 0;</span>

<span class="nc bnc" id="L61" title="All 2 branches missed.">            for (int batch = 0; batch &lt; numBatches; batch++) {</span>
<span class="nc" id="L62">                BatchOperationBuilder builder = new BatchOperationBuilder();</span>

                // Build batch with mixed operations
<span class="nc bnc" id="L65" title="All 2 branches missed.">                for (int i = 0; i &lt; batchSize; i++) {</span>
<span class="nc" id="L66">                    String key = &quot;batch_&quot; + batch + &quot;_key_&quot; + i;</span>
<span class="nc" id="L67">                    String value = &quot;value_&quot; + i;</span>

<span class="nc bnc" id="L69" title="All 2 branches missed.">                    if (i % 3 == 0) {</span>
<span class="nc" id="L70">                        builder.addSet(key, value);</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">                    } else if (i % 3 == 1) {</span>
<span class="nc" id="L72">                        builder.addIncrBy(key, i);</span>
<span class="nc" id="L73">                    } else {</span>
<span class="nc" id="L74">                        builder.addHSet(&quot;hash_&quot; + batch, &quot;field_&quot; + i, value);</span>
                    }
                }

<span class="nc" id="L78">                List&lt;RustyClusterProto.BatchOperation&gt; operations = builder.build();</span>
<span class="nc" id="L79">                List&lt;Boolean&gt; results = client.batchWrite(operations);</span>

<span class="nc bnc" id="L81" title="All 2 branches missed.">                long successCount = results.stream().mapToLong(success -&gt; success ? 1 : 0).sum();</span>
<span class="nc" id="L82">                totalOperations += batchSize;</span>
<span class="nc" id="L83">                logger.info(&quot;Batch {} completed: {}/{} operations successful&quot;,</span>
<span class="nc" id="L84">                    batch + 1, successCount, batchSize);</span>
            }

            // Calculate and log performance statistics
<span class="nc" id="L88">            long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L89">            double durationSeconds = (endTime - startTime) / 1000.0;</span>
<span class="nc" id="L90">            double throughput = totalOperations / durationSeconds;</span>
<span class="nc" id="L91">            logger.info(&quot;Sync batch demo completed: {} operations in {:.2f}s, {:.2f} ops/sec throughput&quot;,</span>
<span class="nc" id="L92">                totalOperations, durationSeconds, throughput);</span>

<span class="nc" id="L94">        } catch (Exception e) {</span>
<span class="nc" id="L95">            logger.error(&quot;Error in sync batch operations demo&quot;, e);</span>
        }
<span class="nc" id="L97">    }</span>

    /**
     * Demonstrate high-throughput asynchronous operations.
     */
    private static void demonstrateAsyncOperations(RustyClusterClientConfig config) {
<span class="nc" id="L103">        logger.info(&quot;=== Asynchronous Operations Demo ===&quot;);</span>

<span class="nc" id="L105">        try (RustyClusterAsyncClient asyncClient = new RustyClusterAsyncClient(config)) {</span>
<span class="nc" id="L106">            int numOperations = 10000;</span>
<span class="nc" id="L107">            List&lt;CompletableFuture&lt;Boolean&gt;&gt; futures = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L108">            long startTime = System.currentTimeMillis();</span>

            // Launch many async operations concurrently
<span class="nc bnc" id="L111" title="All 2 branches missed.">            for (int i = 0; i &lt; numOperations; i++) {</span>
<span class="nc" id="L112">                String key = &quot;async_key_&quot; + i;</span>
<span class="nc" id="L113">                String value = &quot;async_value_&quot; + i;</span>

<span class="nc" id="L115">                CompletableFuture&lt;Boolean&gt; future = asyncClient.setAsync(key, value);</span>
<span class="nc" id="L116">                futures.add(future);</span>
            }

            // Wait for all operations to complete
<span class="nc" id="L120">            CompletableFuture&lt;Void&gt; allFutures = CompletableFuture.allOf(</span>
<span class="nc" id="L121">                futures.toArray(new CompletableFuture[0]));</span>

<span class="nc" id="L123">            allFutures.join();</span>

            // Count successful operations
<span class="nc" id="L126">            long successCount = futures.stream()</span>
<span class="nc" id="L127">                .mapToLong(future -&gt; {</span>
                    try {
<span class="nc bnc" id="L129" title="All 2 branches missed.">                        return future.get() ? 1 : 0;</span>
<span class="nc" id="L130">                    } catch (Exception e) {</span>
<span class="nc" id="L131">                        return 0;</span>
                    }
                })
<span class="nc" id="L134">                .sum();</span>

            // Calculate and log performance statistics
<span class="nc" id="L137">            long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L138">            double durationSeconds = (endTime - startTime) / 1000.0;</span>
<span class="nc" id="L139">            double throughput = numOperations / durationSeconds;</span>

<span class="nc" id="L141">            logger.info(&quot;Async demo completed: {}/{} operations successful in {:.2f}s, {:.2f} ops/sec throughput&quot;,</span>
<span class="nc" id="L142">                successCount, numOperations, durationSeconds, throughput);</span>

<span class="nc" id="L144">        } catch (Exception e) {</span>
<span class="nc" id="L145">            logger.error(&quot;Error in async operations demo&quot;, e);</span>
        }
<span class="nc" id="L147">    }</span>

    /**
     * Demonstrate concurrent operations using multiple threads.
     */
    private static void demonstrateConcurrentOperations(RustyClusterClientConfig config) {
<span class="nc" id="L153">        logger.info(&quot;=== Concurrent Operations Demo ===&quot;);</span>

<span class="nc" id="L155">        int numThreads = 10;</span>
<span class="nc" id="L156">        int operationsPerThread = 1000;</span>
<span class="nc" id="L157">        ExecutorService executor = Executors.newFixedThreadPool(numThreads);</span>

<span class="nc" id="L159">        try (RustyClusterClient client = new RustyClusterClient(config)) {</span>
<span class="nc" id="L160">            boolean authResult = client.authenticate();</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">            if (!authResult) {</span>
<span class="nc" id="L162">                logger.error(&quot;Authentication failed, exiting concurrent demo&quot;);</span>
<span class="nc" id="L163">                return;</span>
            }

<span class="nc" id="L166">            List&lt;CompletableFuture&lt;Void&gt;&gt; threadFutures = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L167">            long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L168">            int totalOperations = numThreads * operationsPerThread;</span>

            // Launch concurrent worker threads
<span class="nc bnc" id="L171" title="All 2 branches missed.">            for (int thread = 0; thread &lt; numThreads; thread++) {</span>
<span class="nc" id="L172">                final int threadId = thread;</span>

<span class="nc" id="L174">                CompletableFuture&lt;Void&gt; future = CompletableFuture.runAsync(() -&gt; {</span>
                    try {
<span class="nc bnc" id="L176" title="All 2 branches missed.">                        for (int op = 0; op &lt; operationsPerThread; op++) {</span>
<span class="nc" id="L177">                            String key = &quot;thread_&quot; + threadId + &quot;_key_&quot; + op;</span>
<span class="nc" id="L178">                            String value = &quot;thread_&quot; + threadId + &quot;_value_&quot; + op;</span>

                            // Mix of different operations
<span class="nc bnc" id="L181" title="All 2 branches missed.">                            if (op % 4 == 0) {</span>
<span class="nc" id="L182">                                client.set(key, value);</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">                            } else if (op % 4 == 1) {</span>
<span class="nc" id="L184">                                client.get(key);</span>
<span class="nc bnc" id="L185" title="All 2 branches missed.">                            } else if (op % 4 == 2) {</span>
<span class="nc" id="L186">                                client.incrBy(key, op);</span>
<span class="nc" id="L187">                            } else {</span>
<span class="nc" id="L188">                                client.hSet(&quot;hash_&quot; + threadId, &quot;field_&quot; + op, value);</span>
                            }
                        }
<span class="nc" id="L191">                        logger.info(&quot;Thread {} completed {} operations&quot;, threadId, operationsPerThread);</span>
<span class="nc" id="L192">                    } catch (Exception e) {</span>
<span class="nc" id="L193">                        logger.error(&quot;Error in thread &quot; + threadId, e);</span>
                    }
<span class="nc" id="L195">                }, executor);</span>

<span class="nc" id="L197">                threadFutures.add(future);</span>
            }

            // Wait for all threads to complete
<span class="nc" id="L201">            CompletableFuture.allOf(threadFutures.toArray(new CompletableFuture[0])).join();</span>

            // Calculate and log final performance statistics
<span class="nc" id="L204">            long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L205">            double durationSeconds = (endTime - startTime) / 1000.0;</span>
<span class="nc" id="L206">            double throughput = totalOperations / durationSeconds;</span>
<span class="nc" id="L207">            logger.info(&quot;Concurrent demo completed: {} operations in {:.2f}s, {:.2f} ops/sec throughput&quot;,</span>
<span class="nc" id="L208">                totalOperations, durationSeconds, throughput);</span>

<span class="nc" id="L210">        } catch (Exception e) {</span>
<span class="nc" id="L211">            logger.error(&quot;Error in concurrent operations demo&quot;, e);</span>
        } finally {
<span class="nc" id="L213">            executor.shutdown();</span>
            try {
<span class="nc bnc" id="L215" title="All 2 branches missed.">                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L216">                    executor.shutdownNow();</span>
                }
<span class="nc" id="L218">            } catch (InterruptedException e) {</span>
<span class="nc" id="L219">                executor.shutdownNow();</span>
<span class="nc" id="L220">                Thread.currentThread().interrupt();</span>
            }
        }
<span class="nc" id="L223">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>