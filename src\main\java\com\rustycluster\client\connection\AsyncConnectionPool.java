package com.rustycluster.client.connection;

import com.rustycluster.client.auth.AuthenticationManager;
import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.client.interceptor.AuthenticationInterceptor;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Asynchronous connection pool for RustyCluster gRPC clients.
 */
public class AsyncConnectionPool implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionPool.class);

    private final RustyClusterClientConfig config;
    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map<String, GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceFutureStub>> stubPools;

    /**
     * Create a new AsyncConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public AsyncConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
        this.config = config;
        this.channelFactory = new GrpcChannelFactory(config);
        this.authenticationManager = authenticationManager;
        this.stubPools = new HashMap<>();

        // Initialize connection pools for each node
        for (NodeConfig nodeConfig : config.getNodes()) {
            GenericObjectPoolConfig<KeyValueServiceGrpc.KeyValueServiceFutureStub> poolConfig = new GenericObjectPoolConfig<>();
            
            // High-throughput optimizations for async operations
            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());
            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());
            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm
            
            // Async-optimized validation
            poolConfig.setTestOnBorrow(false); // Disable for performance
            poolConfig.setTestOnReturn(false); // Disable expensive validation
            poolConfig.setTestWhileIdle(true); // Only validate idle connections
            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30)); // More frequent cleanup
            
            // High-throughput settings for async
            poolConfig.setBlockWhenExhausted(false); // Never block - fail fast
            poolConfig.setMaxWait(java.time.Duration.ofMillis(50)); // Very quick timeout for async
            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer
            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead
            
            // JMX monitoring for production
            poolConfig.setJmxEnabled(true);
            poolConfig.setJmxNamePrefix("RustyClusterAsync-" + nodeConfig.getAddress());

            GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceFutureStub> pool =
                new GenericObjectPool<>(new AsyncStubFactory(nodeConfig), poolConfig);

            stubPools.put(nodeConfig.getAddress(), pool);
            logger.info("Created async connection pool for node: {}", nodeConfig);
        }
    }

    /**
     * Borrow a client stub from the pool for the specified node asynchronously.
     *
     * @param nodeConfig The node configuration
     * @return CompletableFuture that completes with a client stub
     */
    public CompletableFuture<KeyValueServiceGrpc.KeyValueServiceFutureStub> borrowStubAsync(NodeConfig nodeConfig) {
        GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceFutureStub> pool = stubPools.get(nodeConfig.getAddress());
        if (pool == null) {
            return CompletableFuture.failedFuture(
                new IllegalArgumentException("No pool found for node: " + nodeConfig));
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return pool.borrowObject();
            } catch (Exception e) {
                throw new RuntimeException("Failed to borrow stub from pool", e);
            }
        });
    }

    /**
     * Return a client stub to the pool.
     *
     * @param nodeConfig The node configuration
     * @param stub       The client stub to return
     */
    public void returnStub(NodeConfig nodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub stub) {
        GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceFutureStub> pool = stubPools.get(nodeConfig.getAddress());
        if (pool == null) {
            logger.warn("No pool found for node: {}", nodeConfig);
            return;
        }
        pool.returnObject(stub);
    }

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
        for (GenericObjectPool<KeyValueServiceGrpc.KeyValueServiceFutureStub> pool : stubPools.values()) {
            pool.close();
        }
        stubPools.clear();
        logger.info("Async connection pool closed");
    }

    /**
     * Factory for creating and validating async client stubs.
     */
    private class AsyncStubFactory extends BasePooledObjectFactory<KeyValueServiceGrpc.KeyValueServiceFutureStub> {
        private final NodeConfig nodeConfig;

        AsyncStubFactory(NodeConfig nodeConfig) {
            this.nodeConfig = nodeConfig;
        }

        @Override
        public KeyValueServiceGrpc.KeyValueServiceFutureStub create() {
            ManagedChannel channel = channelFactory.createChannel(nodeConfig);

            // Create channel with authentication interceptor
            var interceptedChannel = ClientInterceptors.intercept(channel,
                    new AuthenticationInterceptor(authenticationManager));

            return KeyValueServiceGrpc.newFutureStub(interceptedChannel)
                    .withDeadlineAfter(config.getConnectionTimeoutMs(), TimeUnit.MILLISECONDS);
        }

        @Override
        public PooledObject<KeyValueServiceGrpc.KeyValueServiceFutureStub> wrap(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) {
            return new DefaultPooledObject<>(stub);
        }

        @Override
        public boolean validateObject(PooledObject<KeyValueServiceGrpc.KeyValueServiceFutureStub> p) {
            // For async operations, we'll skip validation for performance
            // In a real implementation, you might want to perform an async health check
            return true;
        }

        @Override
        public void destroyObject(PooledObject<KeyValueServiceGrpc.KeyValueServiceFutureStub> p) {
            KeyValueServiceGrpc.KeyValueServiceFutureStub stub = p.getObject();
            ManagedChannel channel = (ManagedChannel) stub.getChannel();
            try {
                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                logger.warn("Interrupted while shutting down channel", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
